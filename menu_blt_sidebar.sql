-- SQL untuk menambahkan menu Penerimaan BLT ke sidebar
-- Jalankan query ini untuk menambahkan menu ke sidebar aplikasi

-- 1. Tambahkan menu utama Penerimaan BLT
INSERT INTO `msmenu` (`name`, `icon`, `url`, `has_submenu`, `sequence`)
VALUES ('Penerimaan BLT', 'fas fa-money-bill-wave', 'penerimaan_blt', 0, 100);

-- 2. Dapatkan ID menu yang baru saja dibuat (ganti @menu_id dengan ID yang sebenarnya)
SET @menu_id = LAST_INSERT_ID();

-- 3. Berikan akses menu ke semua admin desa yang sudah ada
-- (Opsional: hanya jalankan jika ingin memberikan akses otomatis ke semua admin)
INSERT INTO `accesspermission` (`userid`, `menuid`, `createddate`, `createdby`)
SELECT
    id as userid,
    @menu_id as menuid,
    NOW() as createddate,
    1 as createdby
FROM `msusers`
WHERE `role` = 'admin';

-- ALTERNATIF: Jika ingin menambahkan sebagai submenu dari menu "Program" yang sudah ada
-- Uncomment baris di bawah ini dan comment baris INSERT msmenu di atas

-- INSERT INTO `mssubmenu` (`name`, `icon`, `url`, `menuid`, `sequence`)
-- VALUES ('Penerimaan BLT', 'fas fa-money-bill-wave', 'penerimaan_blt', [ID_MENU_PROGRAM], 100);

-- Kemudian berikan akses submenu:
-- SET @submenu_id = LAST_INSERT_ID();
-- INSERT INTO `accesspermission` (`userid`, `submenuid`, `createddate`, `createdby`)
-- SELECT
--     id as userid,
--     @submenu_id as submenuid,
--     NOW() as createddate,
--     1 as createdby
-- FROM `msusers`
-- WHERE `role` = 'admin';

-- CATATAN PENTING:
-- 1. Pastikan struktur tabel sesuai dengan database Anda
-- 2. Ganti [ID_MENU_PROGRAM] dengan ID menu Program yang sebenarnya jika menggunakan submenu
-- 3. Icon menggunakan Font Awesome: fas fa-money-bill-wave
-- 4. URL: penerimaan_blt (sesuai dengan routing yang sudah dibuat)
-- 5. Sequence 100 untuk menempatkan menu di urutan terakhir
-- 6. Akses otomatis diberikan ke semua user dengan role 'admin'
