# Fitur Bantuan Sosial - Halaman Publik

## Deskripsi
Fitur ini menambahkan halaman publik untuk menampilkan informasi penerima bantuan sosial kepada masyarakat. Fitur ini terintegrasi dengan sistem penerimaan BLT yang sudah ada dan menampilkan data penerima yang telah terverifikasi.

## Fitur yang Ditambahkan

### 1. Menu Navigasi
- **Navbar Desktop**: Menu dropdown "Lainnya" dengan submenu "Bantuan Sosial"
- **Navbar Mobile**: Menu "Bantuan Sosial" di mobile menu
- **Footer Sitemap**: Link "Bantuan Sosial" di sitemap footer

### 2. Halaman Bantuan Sosial (`/bantuan-sosial`)
- **URL**: `/bantuan-sosial`
- **Fungsi**: Menampilkan daftar program bantuan sosial yang tersedia
- **Konten**:
  - Hero section dengan informasi umum
  - Card untuk setiap program bantuan sosial
  - Statistik: Total penerima, terverifikasi, menunggu verifikasi
  - Nominal bantuan per program
  - Tanggal pembuatan program
  - Tombol "Lihat Detail" untuk setiap program

### 3. Halaman Detail Bantuan Sosial (`/bantuan-sosial/detail/{id}`)
- **URL**: `/bantuan-sosial/detail/{id}`
- **Fungsi**: Menampilkan detail program bantuan sosial dan daftar penerima terverifikasi
- **Konten**:
  - Breadcrumb navigation
  - Informasi detail program (nama, nominal, tanggal)
  - Statistik ringkasan (total penerima, dana tersalurkan, nominal per penerima)
  - Tabel daftar penerima terverifikasi (NIK, Nama, Alamat, RT/RW)
  - Informasi kontak untuk pertanyaan lebih lanjut

## Struktur File

### Controller
- `app/application/controllers/BantuanSosial.php`

### Views
#### Tema Default (Landing)
- `app/application/views/bantuan_sosial/index.php`
- `app/application/views/bantuan_sosial/detail.php`

#### Tema Profile V2
- `app/application/views/profile_v2/bantuan_sosial.php`
- `app/application/views/profile_v2/bantuan_sosial_detail.php`

#### Tema ProGides
- `app/application/views/progides/bantuan_sosial.php`
- `app/application/views/progides/bantuan_sosial_detail.php`

### Routing
- Routes ditambahkan di `app/application/config/routes.php`:
  ```php
  $route['bantuan-sosial'] = 'BantuanSosial/index';
  $route['bantuan-sosial/detail/(:num)'] = 'BantuanSosial/detail/$1';
  ```

### Navbar Updates
- `app/application/views/landing/master.php` - Tema default
- `app/application/views/profile_v2/index.php` - Tema profile v2
- `app/application/views/progides/master.php` - Tema ProGides

## Keamanan dan Privasi

### Data yang Ditampilkan
- **Hanya penerima terverifikasi**: Sistem hanya menampilkan data penerima dengan status "terverifikasi"
- **Data publik**: NIK, Nama, Alamat, RT/RW (informasi yang umumnya dapat diakses publik)
- **Tidak menampilkan**: Data sensitif seperti nomor rekening, nomor telepon, dll.

### Kontrol Akses
- **Publik**: Semua halaman dapat diakses tanpa login
- **Filter desa**: Data hanya menampilkan program bantuan sosial dari desa yang bersangkutan
- **Status aktif**: Hanya menampilkan program yang masih aktif

## Responsivitas
- **Desktop**: Layout grid 2 kolom untuk card program
- **Mobile**: Layout single column dengan optimasi touch
- **Tablet**: Layout responsif yang menyesuaikan ukuran layar

## Teknologi yang Digunakan
- **Backend**: CodeIgniter 3
- **Frontend**: 
  - Bootstrap 4/5 (tema default dan profile v2)
  - Tailwind CSS (tema ProGides)
  - Font Awesome icons
  - jQuery
- **Database**: MySQL (menggunakan tabel yang sudah ada)

## Integrasi dengan Sistem Existing
- **Model**: Menggunakan `Penerimaan_BLT_Model` dan `Detail_Penerima_BLT_Model` yang sudah ada
- **Database**: Menggunakan tabel `penerimaan_blt` dan `detail_penerima_blt` yang sudah ada
- **Theme System**: Mendukung semua tema yang tersedia (default, profile_v2, ProGides)
- **Subdomain**: Terintegrasi dengan sistem subdomain yang ada

## Cara Akses
1. **Dari Homepage**: Klik menu "Lainnya" → "Bantuan Sosial"
2. **Direct URL**: `{domain}/bantuan-sosial`
3. **Detail Program**: `{domain}/bantuan-sosial/detail/{id}`

## Catatan Pengembangan
- Fitur ini mengikuti pola MVC yang sudah ada di aplikasi
- Menggunakan helper functions yang sudah tersedia (asset_url, base_url, dll.)
- Responsive design dengan framework CSS yang sesuai tema
- SEO friendly dengan meta tags yang tepat
- Accessibility considerations dengan proper HTML structure

## Perbaikan Error Dropdown
### Masalah
Error `i.createPopper is not a function` terjadi karena:
- Bootstrap 5 memerlukan Popper.js untuk dropdown functionality
- Tema yang berbeda menggunakan versi Bootstrap yang berbeda

### Solusi
1. **Tema Landing (Bootstrap 5)**:
   - Menambahkan Popper.js CDN sebelum Bootstrap.js
   - Implementasi custom dropdown handler dengan jQuery
   - Custom CSS untuk styling dropdown

2. **Tema Profile V2 (Bootstrap 4)**:
   - Menggunakan Bootstrap 4 bundle yang sudah include Popper.js
   - Syntax `data-toggle` untuk Bootstrap 4

3. **Tema ProGides (Tailwind CSS)**:
   - Menggunakan custom JavaScript untuk dropdown functionality
   - Tidak memerlukan Popper.js

## Maintenance
- Data akan otomatis terupdate sesuai dengan perubahan di sistem admin BLT
- Tidak memerlukan maintenance khusus karena menggunakan data yang sudah ada
- Performa optimal karena hanya query data yang diperlukan
- Dropdown functionality telah dioptimasi untuk semua tema
