-- Database Schema untuk Fitur Penerimaan BLT
-- Tabel untuk menyimpan data penerimaan BLT

CREATE TABLE `penerimaan_blt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deskripsi` text NOT NULL,
  `nominal` decimal(15,2) NOT NULL,
  `tanggal_dibuat` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tanggal_diubah` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `id_user` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_id_user` (`id_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabel untuk menyimpan detail penerima BLT
CREATE TABLE `detail_penerima_blt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_penerimaan_blt` int(11) NOT NULL,
  `nik` varchar(16) NOT NULL,
  `status_verifikasi` enum('menunggu_verifikasi','terverifikasi') NOT NULL DEFAULT 'menunggu_verifikasi',
  `tanggal_verifikasi` datetime NULL,
  `tanggal_dibuat` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tanggal_diubah` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_id_penerimaan_blt` (`id_penerimaan_blt`),
  KEY `idx_nik` (`nik`),
  KEY `idx_status_verifikasi` (`status_verifikasi`),
  FOREIGN KEY (`id_penerimaan_blt`) REFERENCES `penerimaan_blt` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`nik`) REFERENCES `warga` (`nik`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
