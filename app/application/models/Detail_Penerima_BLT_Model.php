<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_query_builder $db
 */
class Detail_Penerima_BLT_Model extends MY_Model
{
    protected $table = 'detail_penerima_blt';
    public $SearchDatatables = array('b.nama', 'b.nik', 'a.status_verifikasi');

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.nama, b.alamat, b.rt, b.rw, c.deskripsi as deskripsi_blt, c.nominal')
            ->from($this->table . ' a')
            ->join('warga b', 'b.nik = a.nik')
            ->join('penerimaan_blt c', 'c.id = a.id_penerimaan_blt')
            ->order_by('a.tanggal_dibuat', 'DESC');
    }

    public function getDataWithWarga($where = array())
    {
        $this->db->select('a.*, b.nama, b.alama<PERSON>, b.rt, b.rw, c.desk<PERSON> as deskripsi_blt, c.nominal')
            ->from($this->table . ' a')
            ->join('warga b', 'b.nik = a.nik')
            ->join('penerimaan_blt c', 'c.id = a.id_penerimaan_blt')
            ->order_by('a.tanggal_dibuat', 'DESC');

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get();
    }

    public function updateStatusVerifikasi($id, $status)
    {
        $data = array(
            'status_verifikasi' => $status,
            'tanggal_verifikasi' => ($status == 'terverifikasi') ? getCurrentDate() : null
        );

        return $this->update(array('id' => $id), $data);
    }

    public function deleteByPenerimaanBLT($id_penerimaan_blt)
    {
        return $this->delete(array('id_penerimaan_blt' => $id_penerimaan_blt));
    }
}
