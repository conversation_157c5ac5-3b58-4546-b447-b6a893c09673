<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_query_builder $db
 */
class Penerimaan_BLT_Model extends MY_Model
{
    protected $table = 'penerimaan_blt';
    public $SearchDatatables = array('a.deskripsi', 'a.nominal');

    public function QueryDatatables()
    {
        $this->db->select('a.*, COUNT(b.id) as total_penerima, 
                          SUM(CASE WHEN b.status_verifikasi = "terverifikasi" THEN 1 ELSE 0 END) as total_terverifikasi,
                          SUM(CASE WHEN b.status_verifikasi = "menunggu_verifikasi" THEN 1 ELSE 0 END) as total_menunggu')
            ->from($this->table . ' a')
            ->join('detail_penerima_blt b', 'b.id_penerimaan_blt = a.id', 'LEFT')
            ->group_by('a.id')
            ->order_by('a.tanggal_dibuat', 'DESC');
    }

    public function getDataWithDetails($where = array())
    {
        $this->db->select('a.*, COUNT(b.id) as total_penerima, 
                          SUM(CASE WHEN b.status_verifikasi = "terverifikasi" THEN 1 ELSE 0 END) as total_terverifikasi,
                          SUM(CASE WHEN b.status_verifikasi = "menunggu_verifikasi" THEN 1 ELSE 0 END) as total_menunggu')
            ->from($this->table . ' a')
            ->join('detail_penerima_blt b', 'b.id_penerimaan_blt = a.id', 'LEFT')
            ->group_by('a.id')
            ->order_by('a.tanggal_dibuat', 'DESC');

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get();
    }

    public function getDetailPenerima($id_penerimaan_blt)
    {
        return $this->db->select('a.*, b.nama, b.alamat, b.rt, b.rw')
            ->from('detail_penerima_blt a')
            ->join('warga b', 'b.nik = a.nik')
            ->where('a.id_penerimaan_blt', $id_penerimaan_blt)
            ->order_by('b.nama', 'ASC')
            ->get();
    }
}
