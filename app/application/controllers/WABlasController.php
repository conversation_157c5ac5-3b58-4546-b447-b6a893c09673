<?php
defined('BASEPATH') or die('No direct script access allowed!');

class WABlasController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Config_WABlas', 'config_wablas');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Manage WA Blas';
        $data['content'] = 'wablas/index';
        $data['wablas'] = $this->config_wablas->getDefaultData()->row();

        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not allowed to access this page');
        }

        $domain = getPost('domain');
        $key = getPost('key');

        $get = $this->config_wablas->getDefaultData();

        $exec = array();
        $exec['api_domain'] = $domain;
        $exec['api_key'] = $key;

        if ($get->num_rows() == 0) {
            $this->config_wablas->insert($exec);
        } else {
            $this->config_wablas->update(array(), $exec);
        }

        return JSONResponseDefault('OK', 'Data berhasil diupdate');
    }
}
