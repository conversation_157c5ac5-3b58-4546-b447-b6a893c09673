<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsAutoreplywa $msautoreplywa
 * @property Datatables $datatables
 * @property CI_Upload $upload
 */
class AutoreplywaController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsAutoreplywa', 'msautoreplywa');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Auto Reply WA';
        $data['content'] = 'autoreplywa/index';
        $data['autoreplywa'] = $this->msautoreplywa->result(array(
            'id_user' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('MsAutoreplywa', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.id_user' => getCurrentIdUser())) as $key => $value) {
            $detail = array();
            $detail[] = $value->keyword;
            $detail[] = $value->response;

            $detail[] = "<a href=\"" . base_url('autoreplywa/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteAutoreplywa($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Auto Reply WA';
        $data['content'] = 'autoreplywa/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $keyword = getPost('keyword');
        $response = getPost('response');

        if ($keyword == null) {
            return JSONResponseDefault('FAILED', 'Keyword harus diisi');
        } else if ($response == null) {
            return JSONResponseDefault('FAILED', 'Response harus diisi');
        }

        $insert = array();
        $insert['keyword'] = $keyword;
        $insert['response'] = $response;
        $insert['id_user'] = getCurrentIdUser();

        $this->msautoreplywa->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->msautoreplywa->get(array(
            'id' => $id,
            'id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->msautoreplywa->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msautoreplywa->get(array(
            'id' => $id,
            'id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('asset'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Edit Auto Reply WA';
        $data['content'] = 'autoreplywa/edit';
        $data['autoreplywa'] = $row;

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $keyword = getPost('keyword');
        $response = getPost('response');

        if ($keyword == null) {
            return JSONResponseDefault('FAILED', 'Keyword harus diisi');
        } else if ($response == null) {
            return JSONResponseDefault('FAILED', 'Response harus diisi');
        }

        $get = $this->msautoreplywa->get(array(
            'id' => $id,
            'id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $update = array();
        $update['keyword'] = $keyword;
        $update['response'] = $response;

        $this->msautoreplywa->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}
