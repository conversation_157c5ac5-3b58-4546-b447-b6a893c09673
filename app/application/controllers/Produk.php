<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Produk_Model $produk
 * @property Datatables $datatables
 * @property CI_Upload $upload
 * @property CI_DB_mysqli_driver $db
 * @property Detail_Produk $detailproduk
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 * @property CI_Pagination $pagination
 */
class Produk extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Produk_Model', 'produk');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Detail_Produk', 'detailproduk');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $where = array();
        if (getSessionValue('ROLE') == 'admin') {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        $data['title'] = 'Produk';
        $data['content'] = 'produk/index';
        $data['produk'] = $this->produk->getDefaultData($where)->result();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Produk_Model', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin() || isKecamatan_withWeb()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        foreach ($datatables->getData($where) as $key => $value) {
            if ($value->foto != null) {
                $foto = '<img src="' . asset_url($value->foto) . '" width="100">';
            } else {
                $foto = "- Foto Tidak Ditemukan -";
            }

            $notelp = "<a href=\"https://wa.me/" . convertPhone($value->no_telp) . "\" target=\"_blank\" class=\"btn btn-success btn-sm\">
                <i class=\"fab fa-whatsapp\"></i>
            </a>";

            $detail = array();
            $detail[] = $value->nama;
            $detail[] = $value->kategori;
            $detail[] = 'Rp. ' . IDR($value->harga);
            $detail[] = $value->is_discount == 1 ? $value->discounttype . ' ' . ($value->discounttype == 'Nominal' ? 'Rp' : null) . IDR($value->discount) . ($value->discounttype == 'Persentase' ? '%' : null) : 'Tidak Ada';
            $detail[] = $foto;
            $detail[] = "$notelp
            
            <a href=\"" . base_url('produk/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteProduk($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Produk - Add';
        $data['content'] = 'produk/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $kategori = getPost('kategori');
        $harga = getPost('harga');
        $harga = str_replace(',', '', $harga);
        $harga = str_replace('.', '', $harga);
        $no_telp = getPost('no_telp');
        $deskripsi = getPost('deskripsi');
        $foto = isset($_FILES['foto']['size']) && $_FILES['foto']['size'] > 0 ? $_FILES['foto'] : null;
        $is_discount = getPost('is_discount');
        $discounttype = getPost('discounttype');
        $discount = getPost('discount');

        if ($foto == null) {
            return JSONResponseDefault('FAILED', 'Foto harus diisi');
        }

        $images = array();

        foreach ($foto['name'] as $key => $value) {
            $_FILES['images[]']['name'] = $foto['name'][$key];
            $_FILES['images[]']['type']  = $foto['type'][$key];
            $_FILES['images[]']['tmp_name'] = $foto['tmp_name'][$key];
            $_FILES['images[]']['error'] = $foto['error'][$key];
            $_FILES['images[]']['size'] = $foto['size'][$key];

            try {
                $upload = doUpload_CloudStorage('images[]', 'jpeg|jpg|png');
                $images[] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $insert = array();
        $insert['nama'] = $nama;
        $insert['kategori'] = $kategori;
        $insert['harga'] = $harga;
        $insert['no_telp'] = $no_telp;
        $insert['deskripsi'] = $deskripsi;
        $insert['id_user'] = getCurrentIdUser();
        $insert['foto'] = $images[0];

        if ($is_discount == 1) {
            if (!in_array($discounttype, ['Persentase', 'Nominal'])) {
                return JSONResponseDefault('FAILED', 'Tipe diskon tidak valid');
            } else if (!is_numeric($discount)) {
                return JSONResponseDefault('FAILED', 'Diskon harus berupa angka');
            } else if ($discount < 0) {
                return JSONResponseDefault('FAILED', 'Diskon tidak boleh kurang dari 0');
            } else if ($discounttype == 'Persentase') {
                if ($discount > 100) {
                    return JSONResponseDefault('FAILED', 'Diskon tidak boleh lebih dari 100%');
                }
            }

            $insert['is_discount'] = $is_discount;
            $insert['discounttype'] = $discounttype;
            $insert['discount'] = $discount;
        }

        $doInsert = $this->produk->insert($insert);
        $last_id = $this->db->insert_id();

        foreach ($images as $key => $value) {
            $insert_dt = array();
            $insert_dt['produkid'] = $last_id;
            $insert_dt['foto'] = $value;

            $this->detailproduk->insert($insert_dt);
        }

        if ($doInsert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
        }
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->produk->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->produk->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect('dashboard');
        }

        $get = $this->produk->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('produk');
        }

        $detailproduk = array();
        $detail = $this->detailproduk->result(array('produkid' => $id));

        foreach ($detail as $key => $value) {
            $detailproduk[] = array(
                'name' => $value->foto,
                'url' => asset_url($value->foto)
            );
        }

        $data = array();
        $data['title'] = 'Produk - Edit';
        $data['content'] = 'produk/edit';
        $data['produk'] = $get->row();
        $data['detailproduk'] = json_encode($detailproduk);

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->produk->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $detailproduk = $this->detailproduk->result(array('produkid' => $id));

        $product = array();
        foreach ($detailproduk as $key => $value) {
            $product[$value->foto] = $value->foto;
        }

        $nama = getPost('nama');
        $kategori = getPost('kategori');
        $harga = getPost('harga');
        $harga = str_replace(',', '', $harga);
        $harga = str_replace('.', '', $harga);
        $no_telp = getPost('no_telp');
        $deskripsi = getPost('deskripsi');
        $foto = isset($_FILES['foto']['size']) && $_FILES['foto']['size'] > 0 ? $_FILES['foto'] : null;
        $is_discount = getPost('is_discount');
        $discounttype = getPost('discounttype');
        $discount = getPost('discount');

        $update = array();
        $images = array();

        if ($foto != null && is_array($foto['name']) && count($foto['name']) > 0 && !empty($foto['name'][0])) {
            foreach ($foto['name'] as $key => $value) {
                $_FILES['images[]']['name'] = $foto['name'][$key];
                $_FILES['images[]']['type']  = $foto['type'][$key];
                $_FILES['images[]']['tmp_name'] = $foto['tmp_name'][$key];
                $_FILES['images[]']['error'] = $foto['error'][$key];
                $_FILES['images[]']['size'] = $foto['size'][$key];

                try {
                    $upload = doUpload_CloudStorage('images[]', 'jpeg|jpg|png');
                    $images[] = $upload['name'];
                } catch (Exception $ex) {
                    return JSONResponseDefault('FAILED', $ex->getMessage());
                }
            }

            $update['foto'] = $images[0];
        }

        $update['nama'] = $nama;
        $update['kategori'] = $kategori;
        $update['harga'] = $harga;
        $update['no_telp'] = $no_telp;
        $update['deskripsi'] = $deskripsi;

        if ($is_discount == 1) {
            if (!in_array($discounttype, ['Persentase', 'Nominal'])) {
                return JSONResponseDefault('FAILED', 'Tipe diskon tidak valid');
            } else if (!is_numeric($discount)) {
                return JSONResponseDefault('FAILED', 'Diskon harus berupa angka');
            } else if ($discount < 0) {
                return JSONResponseDefault('FAILED', 'Diskon tidak boleh kurang dari 0');
            } else if ($discounttype == 'Persentase') {
                if ($discount > 100) {
                    return JSONResponseDefault('FAILED', 'Diskon tidak boleh lebih dari 100%');
                }
            }

            $update['is_discount'] = $is_discount;
            $update['discounttype'] = $discounttype;
            $update['discount'] = $discount;
        } else {
            $update['is_discount'] = 0;
            $update['discounttype'] = null;
            $update['discount'] = null;
        }

        $doUpdate = $this->produk->update(array(
            'id' => $id
        ), $update);
        $this->detailproduk->delete(array('produkid' => $id));

        foreach ($images as $key => $value) {
            $insert_dt = array();
            $insert_dt['produkid'] = $id;
            $insert_dt['foto'] = $value;

            $this->detailproduk->insert($insert_dt);
        }

        if ($doUpdate) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function detail()
    {
        $id = getPost('id');

        $get = $this->produk->getDefaultData(array(
            'a.id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data not found');
        }

        return JSONResponse(array(
            'CONTENT' => $get->row()->deskripsi,
            'RESULT' => 'OK'
        ));
    }

    public function belanja($seq = 0)
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        if ($this->subdomain_account->themeid == 2) {
            $q = getGet('q');
            $where_produk = array(
                '(a.kategori = "Makanan" OR a.kategori = "Minuman") =' => true
            );
            $where_kerajinan = array(
                'a.kategori' => 'Kerajinan',
            );
            $where_jasa = array(
                'a.kategori' => 'Jasa',
            );
            $where_pertanian = array(
                'a.kategori' => 'Pertanian',
            );
            $where_peternakan = array(
                'a.kategori' => 'Peternakan',
            );

            if ($this->subdomain_account->admintype == 'Kecamatan') {
                $subdomainaccount_id = $this->subdomain_account->id;
                $subdomainaccount_kecamatan = $this->subdomain_account->kecamatanid;

                $where_produk["(a.id_user = '$subdomainaccount_id' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
                $where_kerajinan["(a.id_user = '$subdomainaccount_id' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
                $where_jasa["(a.id_user = '$subdomainaccount_id' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
                $where_pertanian["(a.id_user = '$subdomainaccount_id' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
                $where_peternakan["(a.id_user = '$subdomainaccount_id' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
            } else {
                $where_produk['a.id_user'] = $this->subdomain_account->id;
                $where_kerajinan['a.id_user'] = $this->subdomain_account->id;
                $where_jasa['a.id_user'] = $this->subdomain_account->id;
                $where_pertanian['a.id_user'] = $this->subdomain_account->id;
                $where_peternakan['a.id_user'] = $this->subdomain_account->id;
            }
        }

        $this->load->library('pagination');

        $config = array();
        $config['base_url'] = base_url('belanja');
        $config['total_rows'] = $this->produk->total($where);
        $config['per_page'] = 12;
        $choice = $config["total_rows"] / $config["per_page"];
        $config["num_links"] = floor($choice);
        $config['first_link'] = 'First';
        $config['last_link'] = 'Last';
        $config['full_tag_open'] = '<div class="pagging text-center"><nav class="mt-2"><ul class="pagination justify-content-end">';
        $config['full_tag_close'] = '</ul></nav></div>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '<span class="sr-only">(current)</span></a></li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tagl_close'] = '<span aria-hidden="true">&raquo;</span></li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tagl_close'] = 'Next</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tagl_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tagl_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');

        $this->pagination->initialize($config);

        $setting = $this->settingumum->getDefaultData($where);
        $data['title'] = 'Produk';
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
        $data['page'] = $seq;
        $data['produk'] = $this->produk->limit($config["per_page"], $data['page'])->getDefaultData($where)->result();
        $data['pagination'] = $this->pagination->create_links();
        $data['kategori_produk'] = $this->produk->select('kategori')->group_by('kategori')->getDefaultData($where)->result();
        $data['user'] = $this->subdomain_account;

        if ($this->subdomain_account->themeid == 2) {
            if ($q == null) {
                $data['produk'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->getDefaultData($where_produk)->result();

                $data['kerajinan'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->getDefaultData($where_kerajinan)->result();

                $data['jasa'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->getDefaultData($where_jasa)->result();

                $data['pertanian'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->getDefaultData($where_pertanian)->result();

                $data['peternakan'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->getDefaultData($where_peternakan)->result();
            } else {
                $data['produk'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->like('LOWER(a.nama)', strtolower($q))
                    ->getDefaultData($where_produk)->result();

                $data['kerajinan'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->like('LOWER(a.nama)', strtolower($q))
                    ->getDefaultData($where_kerajinan)->result();

                $data['jasa'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->like('LOWER(a.nama)', strtolower($q))
                    ->getDefaultData($where_jasa)
                    ->result();

                $data['pertanian'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->like('LOWER(a.nama)', strtolower($q))
                    ->getDefaultData($where_pertanian)->result();

                $data['peternakan'] = $this->produk->select('a.*')
                    ->join('msusers b', 'b.id = a.id_user')
                    ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                    ->like('LOWER(a.nama)', strtolower($q))
                    ->getDefaultData($where_peternakan)->result();
            }
        }

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/belanja';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/belanja';
                return $this->load->view('profile/master', $data);
            } else {
                $data['content'] = 'landing/belanja';
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function deskripsi()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        }

        $id = getPost('id');

        $get = $this->produk->getDefaultData(array(
            'a.id' => $id
        ))->row();

        echo $get->deskripsi;
    }

    public function product_detail($id)
    {
        $data = array();

        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);

        $data['produk'] = $this->db->get_where('produk', array('id' => $id))->row();
        $data['title'] = $data['produk']->nama;
        $data['detail'] = $this->db->get_where('detailproduk', array('produkid' => $id))->result();
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
        $data['other'] = $this->produk->result(array(
            'a.id !=' => $data['produk']->id,
            'a.id_user' => $this->subdomain_account->id,
        ));

        // ProGides platform always uses ProGides theme regardless of theme setting
        if (getPlatformName() == 'ProGides') {
            $data['content'] = 'progides/detail_produk';
            return $this->load->view('progides/master', $data);
        } elseif ($this->subdomain_account->themeid == 2) {
            $data['content'] = 'profile/detail_product';
            return $this->load->view('profile/master', $data);
        } else {
            $data['content'] = 'landing/detail_belanja';
            return $this->load->view('landing/master', $data);
        }
    }

    public function api_category()
    {
        $productcategory = $this->produk->select('kategori')->group_by('kategori')->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $productcategory
        ));
    }

    public function api_latest()
    {
        $product = $this->produk->limit(10)->order_by('a.id', 'DESC')->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $product
        ));
    }

    public function api_all()
    {
        $limit = getGet('limit', 10);
        $offset = getGet('offset', 0);
        $category = getGet('category', 'Makanan');

        $product = $this->produk->limit($limit, $offset)->order_by('a.id', 'DESC')
            ->result(array(
                'a.kategori' => $category
            ));

        return JSONResponse(array(
            'success' => true,
            'data' => $product
        ));
    }
}
