<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Bidang_Perangkat $bidang_perangkat
 */
class BidangPerangkat extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Bidang_Perangkat', 'bidang_perangkat');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Bidang Perangkat';
        $data['content'] = 'bidang_perangkat/index';
        $data['bidangperangkat'] = $this->bidang_perangkat->result(array(
            'createdby' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Bidang Perangkat';
        $data['content'] = 'bidang_perangkat/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $insert = array();
        $insert['name'] = $name;
        $insert['phonenumber'] = $phonenumber;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->bidang_perangkat->insert($insert);

        return JSONResponseDefault('OK', 'Berhasil menambahkan bidang perangkat');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->bidang_perangkat->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data bidang perangkat tidak ditemukan');
        }

        $this->bidang_perangkat->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Berhasil menghapus bidang perangkat');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->bidang_perangkat->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data bidang perangkat tidak ditemukan');
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Edit Bidang Perangkat';
        $data['content'] = 'bidang_perangkat/edit';
        $data['bidangperangkat'] = $row;

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->bidang_perangkat->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data bidang perangkat tidak ditemukan');
        }

        $name = getPost('name');
        $phonenumber = getPost('phonenumber');

        $update = array();
        $update['name'] = $name;
        $update['phonenumber'] = $phonenumber;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->bidang_perangkat->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Berhasil mengubah bidang perangkat');
    }
}
