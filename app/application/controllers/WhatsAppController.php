<?php
defined('BASEPATH') or exit('No direct script access allowed');

class WhatsAppController extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Setting WhatsApp';
        $data['content'] = 'whatsapp/index';

        return $this->load->view('master', $data);
    }

    function qr()
    {
        $sessionid = md5(stringEncryption('encrypt', getSessionWhatsApp()));
        $whatsapp = new WhatsAppHelper();

        $find_session = $whatsapp->find_session($sessionid);

        if (isset($find_session->message) && $find_session->message == 'Session found') {
            $status = $whatsapp->status_session($sessionid);

            if (isset($status->status)) {
                if ($status->status != 'DISCONNECTED' && $status->status != 'CONNECTED') {
                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'SESSION_STATUS' => 'FOUND',
                        'STATUS' => $status->status
                    ));
                } else if ($status->status == 'DISCONNECTED' || $status->status == 'CONNECTED') {
                    if ($status->status == 'DISCONNECTED') {
                        $delete_session = $whatsapp->delete_session($sessionid);

                        if (!isset($delete_session->message) || (isset($delete_session->message) && $delete_session->message != 'Session deleted')) {
                            return JSONResponseDefault('FAILED', 'Gagal menghapus session');
                        }
                    }

                    $createqr = $whatsapp->session_qr($sessionid);

                    if (isset($createqr->qr)) {
                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'SESSION_STATUS' => 'NOT FOUND',
                            'QR' => $createqr->qr
                        ));
                    } else {
                        return JSONResponseDefault('FAILED', 'Gagal membuat QR Code');
                    }
                }
            } else {
                return JSONResponseDefault('FAILED', 'Gagal mendapatkan status session');
            }
        } else {
            $createqr = $whatsapp->session_qr($sessionid);

            if (isset($createqr->qr)) {
                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'SESSION_STATUS' => 'NOT FOUND',
                    'QR' => $createqr->qr
                ));
            } else {
                return JSONResponseDefault('FAILED', 'Gagal membuat QR Code');
            }
        }
    }

    public function check()
    {
        $sessionid = md5(stringEncryption('encrypt', getSessionWhatsApp()));

        $whatsapp = new WhatsAppHelper();

        $find_session = $whatsapp->find_session($sessionid);

        if (isset($find_session->message) && $find_session->message == 'Session found') {
            $session_status = $whatsapp->status_session($sessionid);

            if (isset($session_status->status)) {
                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'STATUS' => $session_status->status
                ));
            } else {
                return JSONResponseDefault('FAILED', 'Gagal mendapatkan status session');
            }
        } else {
            return JSONResponseDefault('FAILED', 'Session tidak ditemukan');
        }
    }

    public function logout()
    {
        $sessionid = md5(stringEncryption('encrypt', getSessionWhatsApp()));

        $whatsapp = new WhatsAppHelper();

        $find_session = $whatsapp->find_session($sessionid);

        if (isset($find_session->message) && $find_session->message == 'Session found') {
            $delete_session = $whatsapp->delete_session($sessionid);

            if (isset($delete_session->message) && $delete_session->message == 'Session deleted') {
                return JSONResponseDefault('OK', 'Berhasil menghapus session');
            } else {
                return JSONResponseDefault('FAILED', 'Gagal menghapus session');
            }
        } else {
            return JSONResponseDefault('FAILED', 'Session tidak ditemukan');
        }
    }
}
