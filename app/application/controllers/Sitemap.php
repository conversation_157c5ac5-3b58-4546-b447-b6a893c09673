<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CI_Output $output
 */
class Sitemap extends MY_Controller
{
    public function index()
    {
        $this->load->helper('xml');

        $dom = xml_dom();
        $urlset = xml_add_child($dom, 'urlset');
        xml_add_attribute($urlset, 'xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        xml_add_attribute($urlset, 'xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        xml_add_attribute($urlset, 'xsi:schemaLocation', 'http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd');

        $baseurl = xml_add_child($urlset, 'url');
        xml_add_child($baseurl, 'loc', base_url());
        xml_add_child($baseurl, 'lastmod', date('Y-m-d'));
        xml_add_child($baseurl, 'changefreq', 'daily');
        xml_add_child($baseurl, 'priority', '1.0');

        $pemerintah = xml_add_child($urlset, 'url');
        xml_add_child($pemerintah, 'loc', base_url('pemerintah'));
        xml_add_child($pemerintah, 'lastmod', date('Y-m-d'));
        xml_add_child($pemerintah, 'changefreq', 'daily');
        xml_add_child($pemerintah, 'priority', '0.8');

        $infografi = xml_add_child($urlset, 'url');
        xml_add_child($infografi, 'loc', base_url('infografi'));
        xml_add_child($infografi, 'lastmod', date('Y-m-d'));
        xml_add_child($infografi, 'changefreq', 'daily');
        xml_add_child($infografi, 'priority', '0.8');

        $belanja = xml_add_child($urlset, 'url');
        xml_add_child($belanja, 'loc', base_url('belanja'));
        xml_add_child($belanja, 'lastmod', date('Y-m-d'));
        xml_add_child($belanja, 'changefreq', 'daily');
        xml_add_child($belanja, 'priority', '0.8');

        // berita/all
        $beritaall = xml_add_child($urlset, 'url');
        xml_add_child($beritaall, 'loc', base_url('berita/all'));
        xml_add_child($beritaall, 'lastmod', date('Y-m-d'));
        xml_add_child($beritaall, 'changefreq', 'daily');
        xml_add_child($beritaall, 'priority', '0.8');

        // surat
        $surat = xml_add_child($urlset, 'url');
        xml_add_child($surat, 'loc', base_url('surat'));
        xml_add_child($surat, 'lastmod', date('Y-m-d'));
        xml_add_child($surat, 'changefreq', 'daily');
        xml_add_child($surat, 'priority', '0.8');

        header('Content-Type: application/xml');
        xml_print($dom);
    }

    public function robots()
    {
        $data = "User-agent: *
Disallow: /cgi-bin/
Disallow: /surat/preview/
Disallow: /surat/create/
Disallow: /surat/switch/
Disallow: /produk/hukum/detail/
Sitemap: " . base_url('sitemap.xml');

        $this->output->set_content_type('text/plain')->set_output($data);
    }
}
