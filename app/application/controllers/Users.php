<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Master_Users $msusers
 */
class Users extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'msusers');
    }

    public function api_kabupaten()
    {
        $kabkota = $this->msusers->select('d.id_kabkota, d.nama_kabkota')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->join('kabkota d', 'd.id_kabkota = c.id_kabkota')
            ->group_by('d.id_kabkota, d.nama_kabkota')
            ->order_by('d.nama_kabkota', 'ASC')
            ->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $kabkota
        ));
    }

    public function api_kecamatan()
    {
        $kabkotaid = getPost('kabkotaid');

        $kecamatan = $this->msusers->select('c.id_kecamatan, c.nama_kecamatan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->join('kecamatan c', 'c.id_kecamatan = b.id_kecamatan')
            ->where(array(
                'b.id_kabkota' => $kabkotaid,
            ))
            ->group_by('c.id_kecamatan, c.nama_kecamatan')
            ->order_by('c.nama_kecamatan', 'ASC')
            ->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $kecamatan
        ));
    }

    public function api_desa()
    {
        $kecamatanid = getPost('kecamatanid');

        $desa = $this->msusers->select('b.id_kelurahan, b.nama_kelurahan, a.subdomain')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->where(array(
                'b.id_kecamatan' => $kecamatanid,
                'a.subdomain !=' => null
            ))
            ->group_by('b.id_kelurahan, b.nama_kelurahan, a.subdomain')
            ->order_by('b.nama_kelurahan', 'ASC')
            ->result();

        return JSONResponse(array(
            'success' => true,
            'data' => $desa
        ));
    }
}
