<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Kategori_Surat $kategori_surat
 * @property DetailKategori_Surat $detail_kategori_surat
 * @property MsLetterAccess $letteraccess
 * @property Master_Users $master_users
 * @property Model_Surat_Access $modelsurataccess
 */
class KategoriSurat extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Kategori_Surat', 'kategori_surat');
        $this->load->model('DetailKategori_Surat', 'detail_kategori_surat');
        $this->load->model('MsLetterAccess', 'letteraccess');
        $this->load->model('Master_Users', 'master_users');
        $this->load->model('Model_Surat_Access', 'modelsurataccess');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Kategori Surat';
        $data['content'] = 'kategori_surat/index';
        $data['kategori'] = $this->letteraccess->select('b.*')
            ->join('kategori_surat b', 'b.id = a.categoryid')
            ->result(array(
                'a.userid' => getCurrentIdUser()
            ));

        return $this->load->view('master', $data);
    }

    public function detail($catsuratid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Detail Kategori Surat';
        $data['content'] = 'kategori_surat/detail';
        $data['detail'] = $this->detail_kategori_surat->getDefaultData(array(
            'a.id_user' => getCurrentIdUser(),
            'a.catsuratid' => $catsuratid
        ))->result();

        return $this->load->view('master', $data);
    }

    public function process_add_detail($suratid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $whatsapp = getPost('whatsapp');

        $insert = array();
        $insert['catsuratid'] = $suratid;
        $insert['nama'] = $nama;
        $insert['jabatan'] = $jabatan;
        $insert['nomor_handphone'] = $whatsapp;
        $insert['id_user'] = getCurrentIdUser();

        $insert = $this->detail_kategori_surat->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan data');
        }
    }

    public function process_delete_detail()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_surat->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->detail_kategori_surat->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function letterscode()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('accessid');

        $access = $this->modelsurataccess->select('a.*, b.modelname AS nama')
            ->join('model_surat b', 'b.id = a.modelid')
            ->get(array(
                'a.id' => $accessid,
                'a.userid' => getCurrentIdUser()
            ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $access->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/model/letterscode', array(
                'user' => getCurrentUser(),
                'access' => $row
            ), true)
        ));
    }

    public function process_letterscode()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('id');
        $letterscode = getPost('letterscode');

        $access = $this->modelsurataccess->get(array(
            'id' => $accessid,
            'userid' => getCurrentIdUser()
        ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->modelsurataccess->update(array(
            'id' => $accessid,
            'userid' => getCurrentIdUser()
        ), array(
            'letterscode' => $letterscode
        ));

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function formatsurat()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('accessid');

        $access = $this->modelsurataccess->select('a.*, b.modelname AS nama')
            ->join('model_surat b', 'b.id = a.modelid')
            ->get(array(
                'a.id' => $accessid,
                'a.userid' => getCurrentIdUser()
            ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $access->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('manageadmin/model/formatsurat', array(
                'user' => getCurrentUser(),
                'access' => $row
            ), true)
        ));
    }

    public function process_formatsurat()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'You are not authorized to access this page');
        }

        $accessid = getPost('id');
        $formatsurat = getPost('formatsurat');

        $access = $this->modelsurataccess->get(array(
            'id' => $accessid,
            'userid' => getCurrentIdUser()
        ));

        if ($access->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->modelsurataccess->update(array(
            'id' => $accessid,
            'userid' => getCurrentIdUser()
        ), array(
            'format_surat' => $formatsurat
        ));

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }
}
