<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Kategori_Berita $kategori_berita
 */
class CategoryNews extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Kategori_Berita', 'kategori_berita');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Kategori Berita';
        $data['content'] = 'kategori_berita/index';
        $data['kategori_berita'] = $this->kategori_berita->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Kategori Berita';
        $data['content'] = 'kategori_berita/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');

        $data = array(
            'nama' => $nama
        );

        $insert = $this->kategori_berita->insert($data);

        if ($insert) {
            return JSONResponseDefault('OK', 'Berhasil menambahkan kategori berita');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan kategori berita');
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $delete = $this->kategori_berita->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Berhasil menghapus kategori berita');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus kategori berita');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->kategori_berita->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/category/news'));
        }

        $data = array();
        $data['title'] = 'Edit Kategori Berita';
        $data['content'] = 'kategori_berita/edit';
        $data['kategori_berita'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->kategori_berita->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kategori berita tidak ditemukan');
        }

        $nama = getPost('nama');

        $data = array(
            'nama' => $nama
        );

        $update = $this->kategori_berita->update(array('id' => $id), $data);

        if ($update) {
            return JSONResponseDefault('OK', 'Berhasil mengubah kategori berita');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah kategori berita');
        }
    }
}
