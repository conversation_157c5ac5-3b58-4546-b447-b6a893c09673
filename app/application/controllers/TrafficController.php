<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Traffic $traffic
 * @property TrafficReport $report
 * @property Master_Users $masterusers
 */
class TrafficController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Traffic', 'traffic');
        $this->load->model('TrafficReport', 'report');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function add()
    {
        $ip = get_client_ip();
        $agent = $_SERVER['HTTP_USER_AGENT'];

        $get = $this->traffic->getDefaultData(['ip_address' => $ip, 'DATE(last_visits)' => getCurrentDate('Y-m-d'), 'userid' => $this->subdomain_account->id]);

        if ($get->num_rows() > 0) {
            $row = $get->row();

            if (getCurrentDate('H:i:s') >= date('H:i:s', strtotime($row->last_visits . " +1hours"))) {
                $update = [];
                $update['last_visits'] = getCurrentDate();
                $update['trafficcount'] = $row->trafficcount + 1;

                $this->traffic->update(array('id' => $row->id), $update);
                $this->add_report($this->subdomain_account->id);
            }
        } else {
            $insert = [];
            $insert['user_agent'] = $agent;
            $insert['ip_address'] = $ip;
            $insert['last_visits'] = getCurrentDate();
            $insert['userid'] = $this->subdomain_account->id;
            $insert['trafficcount'] = 1;

            $this->traffic->insert($insert);
            $this->add_report($this->subdomain_account->id);
        }

        return JSONResponseDefault("SUCCESS", "OK");
    }

    public function add_report($userid)
    {
        $get = $this->report->getDefaultData(['tanggal' => getCurrentDate('Y-m-d'), 'userid' => $userid]);

        if ($get->num_rows() > 0) {
            $data = $get->row();
            $update = [];
            $update['trafficcount'] = $data->trafficcount + 1;

            $this->report->update(array('id' => $data->id), $update);
        } else {
            $insert = [];
            $insert['tanggal'] = getCurrentDate('Y-m-d');
            $insert['trafficcount'] = 1;
            $insert['userid'] = $userid;

            $this->report->insert($insert);
        }
    }

    public function report_daily()
    {
        $desa = getPost('desa');
        $where = array('DATE(last_visits)' => getCurrentDate('Y-m-d'));

        if (getSessionValue('ROLE') == 'admin') {
            $where['userid'] = getCurrentIdUser();
        }

        if ($desa != null) {
            $where['userid'] = $desa;
        }

        $userid = array();
        if (isPMD()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();
            $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        }

        if (count($userid) > 0) {
            $this->traffic->where_in('userid', $userid);
        }

        $traffic = $this->traffic->result($where);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('traffic/report_daily', array(
                'traffic' => $traffic
            ), true)
        ));
    }

    public function report_monthly()
    {
        $desa = getPost('desa');
        $where = array('MONTH(last_visits)' => getCurrentDate('m'), 'YEAR(last_visits)' => getCurrentDate('Y'));

        if (getSessionValue('ROLE') == 'admin') {
            $where['userid'] = getCurrentIdUser();
        }

        if ($desa != null) {
            $where['userid'] = $desa;
        }

        $userid = array();
        if (isPMD()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();
            $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        }

        if (count($userid) > 0) {
            $this->traffic->where_in('userid', $userid);
        }

        $traffic = $this->traffic->result($where);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('traffic/report_monthly', array(
                'traffic' => $traffic
            ), true)
        ));
    }

    public function report_yearly()
    {
        $desa = getPost('desa');
        $where = array('YEAR(last_visits)' => getCurrentDate('Y'));

        if (getSessionValue('ROLE') == 'admin') {
            $where['userid'] = getCurrentIdUser();
        }

        if ($desa != null) {
            $where['userid'] = $desa;
        }

        $userid = array();
        if (isPMD()) {
            $user = $this->masterusers->getDefaultData(array('id' => getCurrentIdUser()))->row();
            $kelurahan = $this->db->get_where('kelurahan', array('id_kabkota' => $user->kabkotaid))->result();

            $kelurahanid = array();
            foreach ($kelurahan as $key => $value) {
                $kelurahanid[] = $value->id_kelurahan;
            }

            $users = $this->db->where_in('kelurahanid', $kelurahanid)->get('msusers')->result();

            foreach ($users as $key => $value) {
                $userid[] = $value->id;
            }
        }

        if (count($userid) > 0) {
            $this->traffic->where_in('userid', $userid);
        }

        $traffic = $this->traffic->result($where);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('traffic/report_yearly', array(
                'traffic' => $traffic
            ), true)
        ));
    }
}
