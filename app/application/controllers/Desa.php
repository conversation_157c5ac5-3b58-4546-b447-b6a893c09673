<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Desa extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Master_Users', 'master_users');
    }

    public function api_login()
    {
        $username = getPost('username');
        $password = getPost('password');

        $get = $this->master_users->select('b.logo_desa AS foto, b.desa, b.kecamatan, b.kabupaten, b.alama<PERSON>, b.nama, b.jabatan')
            ->join('setting_umum b', 'b.id_user = a.id')
            ->getDefaultData(array(
                'username' => $username,
                'password' => md5($password)
            ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Username atau password salah');
        }

        $data = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Login Berhasil',
            'DATA' => $data
        ));
    }

    public function api_logo_download()
    {
        $this->load->helper('download');
        $filename = getGet('filename');

        return force_download('uploads/' . $filename, null);
    }
}
