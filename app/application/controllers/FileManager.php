<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsAsset $asset
 */
class FileManager extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsAsset', 'asset');
    }

    public function browse()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['asset'] = $this->asset->result(array('id_user' => getCurrentIdUser()));

        return $this->load->view('filemanager/browse', $data);
    }
}
