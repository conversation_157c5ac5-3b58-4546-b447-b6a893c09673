<?php
defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpWord\TemplateProcessor;
use Spipu\Html2Pdf\Html2Pdf;

/**
 * @property Kategori_Surat $kategorisurat
 * @property Model_Surat $lettersmodels
 * @property Field_Surat $fieldsurat
 * @property DefaultValue_Model $defaultvaluefield
 * @property Master_Users $masterusers
 * @property Model_Surat_Access $modelsurataccess
 * @property CI_Upload $upload
 * @property CI_DB_mysqli_driver $db
 */
class CategoryLetters extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Kategori_Surat', 'kategorisurat');
        $this->load->model('Model_Surat', 'lettersmodels');
        $this->load->model('Field_Surat', 'fieldsurat');
        $this->load->model('DefaultValue_Model', 'defaultvaluefield');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Model_Surat_Access', 'modelsurataccess');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $where = array();

        if (!isAllPlatform()) {
            $where['a.platformname'] = getCurrentPlatformName();
        }

        $data = array();
        $data['title'] = 'Kategori Surat';
        $data['content'] = 'kategori_surat/index';
        $data['kategori'] = $this->kategorisurat->result($where);

        return $this->load->view('master', $data);
    }

    public function models($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->kategorisurat->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/category/letters'));
        }

        $where = array('role' => 'admin');

        if (!isAllPlatform()) {
            $where['a.platformname'] = getCurrentPlatformName();
        }

        $data = array();
        $data['title'] = 'Model Surat';
        $data['content'] = 'kategori_surat/models';
        $data['model'] = $this->lettersmodels->result(array('categorylettersid' => $id));
        $data['desa'] = $this->masterusers->select('a.id, b.nama_kelurahan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->result($where);
        $data['kategorisurat'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_models($categorylettersid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->kategorisurat->get(array('id' => $categorylettersid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kategori surat tidak ditemukan');
        }

        $modelname = getPost('modelname');
        $lettersmethod = getPost('lettersmethod');
        $useraccess = getPost('useraccess');

        $insert = array();
        $insert['categorylettersid'] = $categorylettersid;
        $insert['modelname'] = $modelname;
        $insert['modelformat'] = '<table border="0" cellpadding="0" cellspacing="0" style="width:608px"><tbody><tr><td style="width:179px"><img alt="" src="https://asset.gides.id/51c085483340c0c286c08992fe65bb0e.png" style="float:left; height:115px; margin-left:50px; margin-right:50px; width:100px"/></td><td style="text-align:center; width:400px"><h2><span style="font-size:20px">PEMERINTAHAN ${kabupaten_desa}</span></h2><p><span style="font-size:20px"><strong>${kecamatan_desa}</strong></span></p><p><span style="font-size:20px"><strong>${nama_desa}</strong></span></p><p>&nbsp;</p><p><span style="font-size:11px">${alamat_desa}</span></p></td></tr></tbody></table><p>&nbsp;</p><hr/><p>&nbsp;</p><p style="text-align:center"><span style="font-size:18px"><u><strong>SURAT KETERANGAN</strong></u></span></p><p style="text-align:center"><span style="font-size:16px">Nomor Surat: ${nomor_surat}</span></p><p style="text-align:center">&nbsp;</p><p><span style="font-size:16px">Yang bertanda tangan dibawah ini ${nama_penandatangan}${nama_desa}, ${kabupaten_desa}</span></p><p><span style="font-size:16px">menerangkan bahwa:</span></p><p>&nbsp;</p><p><span style="font-size:16px">Menerangkan dengan sebenarnya bahwa:</span></p><table border="0" cellpadding="0" cellspacing="0" style="width:692px"><tbody><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">Nama</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${nama}</span></td></tr><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">NIK</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${nik}</span></td></tr><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">Tempat, Tanggal Lahir</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${tempat_lahir}, ${tanggal_lahir}</span></td></tr><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">Jenis Kelamin</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${jenis_kelamin}</span></td></tr><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">Pekerjaan</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${pekerjaan}</span></td></tr><tr><td style="width:21px">&nbsp;</td><td style="width:211px"><span style="font-size:16px">Alamat</span></td><td style="width:11px"><span style="font-size:16px">:</span></td><td style="width:439px"><span style="font-size:16px">${alamat}&nbsp;</span></td></tr></tbody></table><p>&nbsp;</p><p><span style="font-size:16px">Yang tersebut namanya di atas benar merupakan</span></p><p><span style="font-size:16px">Demikian Surat ini dibuat dan diberikan kepadanya untuk digunakan seperlu.</span></p><p>&nbsp;</p><table border="0" cellpadding="0" cellspacing="0" style="width:695px"><tbody><tr><td style="width:371px">&nbsp;</td><td style="width:313px"><p style="text-align:right">${nama_desa}, ${tanggal_permohonan}</p><p style="text-align:right">${jabatan_penandatangan}</p><p style="text-align:right">&nbsp;</p><p style="text-align:right">&nbsp;</p><p style="text-align:right">&nbsp;</p><p style="text-align:right">&nbsp;</p><p style="text-align:right">&nbsp;</p><p style="text-align:right">(${nama_penandatangan})</p></td></tr></tbody></table><p>&nbsp;</p>';
        $insert['lettermethod'] = $lettersmethod;

        if ($lettersmethod == 'Upload') {
            try {
                $upload = doUpload_CloudStorage('letterformat', 'docx');
                $insert['letterformat'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $this->lettersmodels->insert($insert);
        $modelid = $this->db->insert_id();

        foreach (explode(',', $useraccess) as $key => $value) {
            $insert = array();
            $insert['userid'] = $value;
            $insert['modelid'] = $modelid;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->modelsurataccess->insert($insert);
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Model surat berhasil ditambahkan',
            'LETTERMETHOD' => $lettersmethod,
            'REDIRECT' => base_url("manage/category/letters/models/$categorylettersid/builder/$modelid")
        ));
    }

    public function full_builder($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return redirect(base_url("manage/category/letters/models/$categorylettersid"));
        }

        $row = $get->row();

        $data = array();
        $data['model'] = $row;
        $data['fields'] = $this->fieldsurat->result(array('modelid' => $modelid));

        return $this->load->view('full_builder', $data);
    }

    public function preview_builder($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return redirect(base_url("manage/category/letters/models/$categorylettersid"));
        }

        $row = $get->row();

        $formatted = $row->modelformat;

        $field_static = $this->defaultvaluefield->result(array('modelid' => $modelid));
        $field_dynamic = $this->fieldsurat->result(array('modelid' => $modelid));

        foreach ($field_static as $key => $value) {
            $var = "$" . "{" . $value->parameter . "}";
            $formatted = str_replace($var, strtoupper(($value->val != null ? $value->val : $value->parameter)), $formatted);
        }

        $pdf = new Html2Pdf('P', 'A4', 'fr', true, 'UTF-8', array(15, 15, 15, 15));
        $pdf->setTestTdInOnePage(false);
        $pdf->writeHTML($this->load->view('preview_surat', array(
            'formatted' => $formatted,
            'field_static' => $field_static,
            'field_dynamic' => $field_dynamic
        ), true));
        $pdf->output('Surat.pdf');
    }

    public function builder($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return redirect(base_url("manage/category/letters/models/$categorylettersid"));
        }

        $row = $get->row();

        $access = $this->modelsurataccess->result(array(
            'modelid' => $modelid
        ));
        $useraccess = array();

        foreach ($access as $key => $value) {
            $useraccess[] = $value->userid;
        }

        $data = array();
        $data['title'] = 'Builder Surat - ' . $row->modelname;
        $data['content'] = 'kategori_surat/builder';
        $data['model'] = $row;
        $data['fields'] = $this->fieldsurat->result(array('modelid' => $modelid));
        $data['desa'] = $this->masterusers->select('a.id, b.nama_kelurahan')
            ->join('kelurahan b', 'b.id_kelurahan = a.kelurahanid')
            ->result(array('role' => 'admin'));
        $data['useraccess'] = $useraccess;

        return $this->load->view('master', $data);
    }

    public function process_edit_format($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
        }

        $update = array();
        $update['modelformat'] = getPost('modelformat');

        $this->lettersmodels->update(array('id' => $modelid), $update);

        return JSONResponseDefault('OK', 'Model surat berhasil diubah');
    }

    public function process_edit_builder($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
        }

        $update = array();
        $update['modelname'] = getPost('modelname');
        $update['modelformat'] = getPost('modelformat');

        $this->lettersmodels->update(array('id' => $modelid), $update);

        $useraccess = explode(',', getPost('useraccess'));
        $access = $this->modelsurataccess->result(array(
            'modelid' => $modelid
        ));

        $useraccessdb = array();

        foreach ($access as $key => $value) {
            $useraccessdb[] = $value->userid;
        }

        $useraccessdiff = array_diff($useraccessdb, $useraccess);

        foreach ($useraccessdiff as $key => $value) {
            $this->modelsurataccess->delete(array(
                'modelid' => $modelid,
                'userid' => $value
            ));
        }

        $useraccessdiff = array_diff($useraccess, $useraccessdb);

        foreach ($useraccessdiff as $key => $value) {
            $this->modelsurataccess->insert(array(
                'modelid' => $modelid,
                'userid' => $value
            ));
        }

        return JSONResponseDefault('OK', 'Model surat berhasil diubah');
    }

    public function process_delete_builder($categorylettersid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $modelid = getPost('id');

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
        }

        $this->lettersmodels->delete(array('id' => $modelid));
        $this->fieldsurat->delete(array('modelid' => $modelid));

        return JSONResponseDefault('OK', 'Model surat berhasil dihapus');
    }

    public function fields($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/category/letters/models/' . $categorylettersid));
        }

        $data = array();
        $data['title'] = 'Field Surat';
        $data['content'] = 'kategori_surat/fields';
        $data['model'] = $get->row();
        $data['fields'] = $this->fieldsurat->order_by('sequence', 'ASC')->result(array('modelid' => $modelid));

        return $this->load->view('master', $data);
    }

    public function process_add_fields($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
        }

        $variable = getPost('variable');
        $label = getPost('label');
        $type = getPost('type');
        $required = getPost('required');
        $sequence = getPost('sequence');
        $item = getPost('item');
        $isstatement = getPost('isstatement');
        $statement = getPost('statement');
        $statementtype = getPost('statementtype');
        $statementwith = getPost('statementwith');
        $statementvalid = getPost('statementvalid');
        $statementinvalid = getPost('statementinvalid');
        $helpertext = getPost('helpertext');
        $helpertextcontent = getPost('helpertextcontent');

        if (in_array($variable, defaultVariable())) {
            return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan');
        }

        $validate = $this->fieldsurat->get(array(
            'modelid' => $modelid,
            'LOWER(variablename) =' => strtolower($variable)
        ));

        if ($validate->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan');
        }

        $insert = array();
        $insert['modelid'] = $modelid;
        $insert['variablename'] = str_replace(' ', '', strtolower($variable));

        if ($isstatement != 'on') {
            $insert['input_type'] = $type;
            $insert['input_label'] = $label;
            $insert['is_required'] = $required ? 1 : 0;
            $insert['sequence'] = $sequence;

            if ($type == 'select') {
                if ($item == null) {
                    return JSONResponseDefault('FAILED', 'Item harus diisi');
                }

                $json = array();
                $explode = explode(',', $item);
                foreach ($explode as $key => $value) {
                    $json[] = trim($value);
                }

                $insert['other'] = json_encode($json);
            } else if ($type == 'multiple') {
                $style = getPost('style');
                $detail_variable = getPost('detail_variable', []);
                $detail_label = getPost('detail_label', []);
                $detail_type = getPost('detail_type', []);
                $detail_required = getPost('detail_required', []);

                $insert['style'] = $style;

                $others = array();
                foreach ($detail_variable as $key => $value) {
                    if (in_array($value, defaultVariable())) {
                        return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan: ' . $value);
                    }

                    $others[] = array(
                        'variable_name' => $value,
                        'input_label' => $detail_label[$key],
                        'input_type' => $detail_type[$key],
                        'is_required' => $detail_required[$key] ? 1 : 0
                    );
                }

                $insert['other'] = json_encode($others);
            }
        } else {
            $insert['isstatement'] = 1;
            $insert['statement'] = json_encode(array(
                'statement' => $statement,
                'type' => $statementtype,
                'with' => $statementwith,
                'valid' => $statementvalid,
                'invalid' => $statementinvalid
            ));

            $insert['input_type'] = null;
            $insert['input_label'] = null;
            $insert['is_required'] = null;
            $insert['sequence'] = null;
            $insert['style'] = null;
            $insert['other'] = null;
        }

        if ($helpertext == 'on') {
            $insert['helpertext'] = $helpertextcontent;
        } else {
            $insert['helpertext'] = null;
        }

        $this->fieldsurat->insert($insert);

        return JSONResponseDefault('OK', 'Field surat berhasil ditambahkan');
    }

    public function process_delete_fields($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->fieldsurat->get(array(
            'modelid' => $modelid,
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Field surat tidak ditemukan');
        }

        $this->fieldsurat->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Field surat berhasil dihapus');
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Kategori Surat';
        $data['content'] = 'kategori_surat/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $platformname = getPost('platformname');

        if (!isAllPlatform()) {
            $platformname = getCurrentPlatformName();
        }

        if ($platformname != null && !in_array($platformname, ['Gides', 'GidesManis', 'SmartDes', 'ProGides'])) {
            return JSONResponseDefault('FAILED', 'Platform tidak valid');
        }

        try {
            $upload = doUpload_CloudStorage('file', 'jpg|jpeg|png|svg');
            $file_name = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $insert = array();
        $insert['nama'] = $nama;
        $insert['icon'] = $file_name;
        $insert['platformname'] = $platformname;

        $this->kategorisurat->insert($insert);

        return JSONResponseDefault('OK', 'Kategori surat berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->kategorisurat->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/category/letters'));
        }

        $data = array();
        $data['title'] = 'Edit Kategori Surat';
        $data['content'] = 'kategori_surat/edit';
        $data['category'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->kategorisurat->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kategori surat tidak ditemukan');
        }

        $nama = getPost('nama');
        $platformname = getPost('platformname');

        if (!isAllPlatform()) {
            $platformname = getCurrentPlatformName();
        }

        if ($platformname != null && !in_array($platformname, ['Gides', 'GidesManis', 'SmartDes', 'ProGides'])) {
            return JSONResponseDefault('FAILED', 'Platform tidak valid');
        }

        $file_name = null;
        if (isset($_FILES['file']['size']) && $_FILES['file']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('file', 'jpg|jpeg|png|svg');
                $file_name = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $update = array();
        $update['nama'] = $nama;
        $update['platformname'] = $platformname;

        if ($file_name != null) {
            $update['icon'] = $file_name;
        }

        $this->kategorisurat->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Kategori surat berhasil diubah');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->kategorisurat->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Kategori surat tidak ditemukan');
        }

        $this->kategorisurat->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Kategori surat berhasil dihapus');
    }

    public function edit_default_value($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');
        $state = getPost('state');

        if ($state == 'dynamic') {
            $get = $this->fieldsurat->get(array(
                'modelid' => $modelid,
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
            }
        } else {
            $get = $this->defaultvaluefield->get(array(
                'modelid' => $modelid,
                'parameter' => $id
            ));
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_surat/edit_default_value', array(
                'model' => $row,
                'state' => $state,
                'id' => $id,
            ), true)
        ));
    }

    public function process_edit_value($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');
        $isi = getPost('isi');
        $state = getPost('state');

        if ($state == 'dynamic') {
            $get = $this->fieldsurat->get(array(
                'modelid' => $modelid,
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
            }

            $update = array();
            $update['default_value'] = $isi;

            $this->fieldsurat->update(array(
                'id' => $id
            ), $update);
        } else {
            $get = $this->defaultvaluefield->get(array(
                'parameter' => $id,
                'modelid' => $modelid
            ));

            $execute = array();
            $execute['modelid'] = $modelid;
            $execute['parameter'] = $id;
            $execute['val'] = $isi;

            if ($get->num_rows() == 0) {
                $this->defaultvaluefield->insert($execute);
            } else {
                $this->defaultvaluefield->update(array(
                    'modelid' => $modelid,
                    'parameter' => $id
                ), $execute);
            }
        }

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function edit_format_surat($categoryid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengakses halaman ini');
        }

        $id = getPost('id');

        $get = $this->lettersmodels->get(array(
            'id' => $id,
            'categorylettersid' => $categoryid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_surat/edit_builder', array(
                'model' => $row
            ), true)
        ));
    }

    public function process_edit_format_surat($categoryid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengakses halaman ini');
        }

        $id = getPost('id');

        $get = $this->lettersmodels->get(array(
            'id' => $id,
            'categorylettersid' => $categoryid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        try {
            $upload = doUpload_CloudStorage('format', 'docx');
            $file_name = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $update = array();
        $update['letterformat'] = $file_name;

        $this->lettersmodels->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Format surat berhasil diubah');
    }

    public function preview($categoryid, $modelid)
    {
        $get = $this->lettersmodels->get(array(
            'id' => $modelid,
            'categorylettersid' => $categoryid
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/category/letters/models/' . $categoryid));
        }

        $row = $get->row();

        if ($row->lettermethod != 'Upload') {
            return redirect(base_url('manage/category/letters/models/' . $categoryid));
        }

        $convert = convertWordToPDF($row->letterformat);

        // remove extension
        $filename = explode('.', $row->letterformat);
        $filename = $filename[0];

        if ($convert && !isset($convert->error)) {
            return redirect(asset_url($filename . '.pdf'));
        } else {
            return redirect(asset_url($row->letterformat));
        }
    }

    public function edit_fields($categoryid, $modelid)
    {
        $id = getPost('id');

        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengakses halaman ini');
        }

        $get = $this->fieldsurat->get(array(
            'id' => $id,
            'modelid' => $modelid,
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_surat/edit_fields', array(
                'model' => $row,
                'statement' => json_decode($row->statement ?? '')
            ), true)
        ));
    }

    public function process_edit_fields($categorylettersid, $modelid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengakses halaman ini');
        }

        $get = $this->lettersmodels->get(array('categorylettersid' => $categorylettersid, 'id' => $modelid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Model surat tidak ditemukan');
        }

        $id = getPost('id');
        $variable = getPost('variable');
        $label = getPost('label');
        $type = getPost('type');
        $required = getPost('required');
        $sequence = getPost('sequence');
        $item = getPost('item');
        $isstatement = getPost('isstatement');
        $statement = getPost('statement');
        $statementtype = getPost('statementtype');
        $statementwith = getPost('statementwith');
        $statementvalid = getPost('statementvalid');
        $statementinvalid = getPost('statementinvalid');
        $helpertext = getPost('helpertext');
        $helpertextcontent = getPost('helpertextcontent');

        if (in_array($variable, defaultVariable())) {
            return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan');
        }

        $validate = $this->fieldsurat->get(array(
            'modelid' => $modelid,
            'LOWER(variablename) =' => strtolower($variable),
            'id !=' => $id
        ));

        if ($validate->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan');
        }

        $update = array();
        $update['modelid'] = $modelid;
        $update['variablename'] = str_replace(' ', '', strtolower($variable));

        if ($isstatement != 'on') {
            $update['input_type'] = $type;
            $update['input_label'] = $label;
            $update['is_required'] = $required ? 1 : 0;
            $update['sequence'] = $sequence;

            if ($type == 'select') {
                if ($item == null) {
                    return JSONResponseDefault('FAILED', 'Item harus diisi');
                }

                $json = array();
                $explode = explode(',', $item);
                foreach ($explode as $key => $value) {
                    $json[] = trim($value);
                }

                $update['style'] = null;
                $update['other'] = json_encode($json);
            } else if ($type == 'multiple') {
                $style = getPost('style');
                $detail_variable = getPost('detail_variable', []);
                $detail_label = getPost('detail_label', []);
                $detail_type = getPost('detail_type', []);
                $detail_required = getPost('detail_required', []);

                $update['style'] = $style;

                $others = array();
                foreach ($detail_variable as $key => $value) {
                    if (in_array($value, defaultVariable())) {
                        return JSONResponseDefault('FAILED', 'Nama variabel sudah digunakan: ' . $value);
                    }

                    $others[] = array(
                        'variable_name' => $value,
                        'input_label' => $detail_label[$key],
                        'input_type' => $detail_type[$key],
                        'is_required' => $detail_required[$key] ? 1 : 0
                    );
                }

                $update['other'] = json_encode($others);
            }

            $update['isstatement'] = 0;
        } else {
            $update['isstatement'] = 1;
            $update['statement'] = json_encode(array(
                'statement' => $statement,
                'type' => $statementtype,
                'with' => $statementwith,
                'valid' => $statementvalid,
                'invalid' => $statementinvalid
            ));

            $update['input_type'] = null;
            $update['input_label'] = null;
            $update['is_required'] = null;
            $update['sequence'] = null;
            $update['style'] = null;
            $update['other'] = null;
        }

        if ($helpertext == 'on') {
            $update['helpertext'] = $helpertextcontent;
        } else {
            $update['helpertext'] = null;
        }

        $this->fieldsurat->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }
}
