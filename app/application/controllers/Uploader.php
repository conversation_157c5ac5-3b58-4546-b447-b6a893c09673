<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsAsset $asset
 * @property CI_Upload $upload
 */
class Uploader extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsAsset', 'asset');
    }

    public function upload()
    {
        if (!isLogin()) {
            echo json_encode(array(
                'uploaded' => 0,
                'error' => array(
                    'message' => 'You are not logged in'
                )
            ));
            exit;
        }

        if (get_cookie('ckCsrfToken') == getPost('ckCsrfToken')) {
            try {
                $upload = doUpload_CloudStorage('upload', 'jpg|jpeg|png|gif');

                $insert = array();
                $insert['assetname'] = $upload['name'];
                $insert['assetfile'] = $upload['name'];
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();
                $insert['id_user'] = getCurrentIdUser();

                $this->asset->insert($insert);

                echo json_encode(array(
                    'uploaded' => 1,
                    'fileName' => $upload['name'],
                    'url' => asset_url($upload['name'])
                ));
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        } else {
            echo json_encode(array(
                'uploaded' => 0,
                'error' => array(
                    'message' => 'Invalid CSRF Token'
                )
            ));
        }
    }
}
