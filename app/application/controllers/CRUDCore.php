<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CRUD $crud
 */
class CRUDCore extends MY_Controller
{
    public function index($feature)
    {
        if (!isLogin()) {
            return redirect(base_url('login'));
        }

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');

        $data = array();
        $data['title'] = ucwords($feature);
        $data['content'] = $this->crud->showContents($feature, 'index');
        $data['element'] = $this->crud->getElements(getContents($feature, 'index'));

        return $this->load->view('master', $data);
    }

    public function add($feature)
    {
        if (!isLogin()) {
            return redirect(base_url('login'));
        }

        $data = array();
        $data['title'] = 'Tambah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'add');
        $data['element'] = $this->crud->getElements(getContents($feature, 'add'));

        return $this->load->view('master', $data);
    }

    public function process_add($feature)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi anda telah habis silahkan muat ulang halaman ini');
        }

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $insert = array();
        foreach ($_FILES as $key => $value) {
            if ($value['name'] == null) continue;

            try {
                $upload = doUpload_CloudStorage($key, 'jpg|jpeg|png|gif');
                $file_name = $upload['name'];

                $insert[$key] = $file_name;
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        foreach ($_POST as $key => $value) {
            if (str_contains($key, 'validated')) {
                $col = explode('_', $key);

                $validate = $this->$models->get(array($col[1] => getPost($key)));

                if ($validate->num_rows() > 0) {
                    return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                }
            }

            if (getPost($key) != null) {
                if (str_contains($key, 'password')) {
                    $insert[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                } else {
                    if (str_contains($key, 'validated')) {
                        $col = explode('_', $key);

                        $insert[$col[1]] = getPost($key);
                    } else {
                        $insert[$key] = getPost($key);
                    }
                }
            }
        }

        $this->$models->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function edit($feature, $id)
    {
        if (!isLogin()) {
            return redirect(base_url('login'));
        }

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/' . $feature));
        }

        $data = array();
        $data['title'] = 'Ubah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'edit');
        $data['element'] = $this->crud->getElements(getContents($feature, 'edit', $get->row()));

        return $this->load->view('master', $data);
    }

    public function process_edit($feature, $id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi anda telah habis silahkan muat ulang halaman ini');
        }

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $update = array();
        foreach ($_FILES as $key => $value) {
            if ($value['name'] == null) continue;

            try {
                $upload = doUpload_CloudStorage($key, 'jpg|jpeg|png|gif');
                $file_name = $upload['name'];

                $update[$key] = $file_name;
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        foreach ($_POST as $key => $value) {
            if (str_contains($key, 'validated')) {
                $col = explode('_', $key);

                $validate = $this->$models->get(array($col[1] => getPost($key)));

                if ($validate->num_rows() > 0 && $get->row()->{$col[1]} != getPost($key)) {
                    return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                }
            }

            if (getPost($key) != null) {
                if (str_contains($key, 'password')) {
                    $update[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                } else {
                    if (str_contains($key, 'validated')) {
                        $col = explode('_', $key);

                        $update[$col[1]] = getPost($key);
                    } else {
                        $update[$key] = getPost($key);
                    }
                }
            }
        }

        $this->$models->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function process_delete($feature)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi anda telah habis silahkan muat ulang halaman ini');
        }

        $id = getPost('id');

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->$models->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}
