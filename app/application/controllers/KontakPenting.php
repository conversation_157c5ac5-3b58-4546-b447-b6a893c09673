<?php
defined('BASEPATH') or die('No direct script access sallowed!');

class KontakPenting extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Kontak_Penting', 'kontakpenting');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $where = array();
        if (getSessionValue('ROLE') == 'admin') {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        $data['title'] = 'Kontak Penting';
        $data['content'] = 'kontakpenting/index';
        $data['kontak'] = $this->kontakpenting->getDefaultData($where)->result();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Kontak_Penting', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        foreach ($datatables->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->nama;
            $detail[] = $value->no_telp;
            $detail[] = "<a href=\"" . base_url('kontakpenting/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteKontak($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Kontak Penting - Add';
        $data['content'] = 'kontakpenting/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $nama = getPost('nama');
        $no_telp = getPost('no_telp');
        $notifikasi = getPost('notifikasi', 0);

        $insert = array();
        $insert['nama'] = $nama;
        $insert['no_telp'] = $no_telp;
        $insert['id_user'] = getCurrentIdUser();
        $insert['has_notification'] = $notifikasi ? 1 : 0;

        $doInsert = $this->kontakpenting->insert($insert);

        if ($doInsert) {
            return JSONResponseDefault('OK', 'Kontak berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan kontak');
        }
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->kontakpenting->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->kontakpenting->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $kontak = $this->kontakpenting->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($kontak->num_rows() == 0) {
            return redirect('kontakpenting');
        }

        $data = array();
        $data['title'] = 'Kontak Penting - Edit';
        $data['content'] = 'kontakpenting/edit';
        $data['kontak'] = $kontak->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->kontakpenting->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $nama = getPost('nama');
        $no_telp = getPost('no_telp');
        $notifikasi = getPost('notifikasi', 0);

        $update = array();
        $update['nama'] = $nama;
        $update['no_telp'] = $no_telp;
        $update['has_notification'] = $notifikasi ? 1 : 0;

        $doUpdate = $this->kontakpenting->update(array(
            'id' => $id
        ), $update);

        if ($doUpdate) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }
}
