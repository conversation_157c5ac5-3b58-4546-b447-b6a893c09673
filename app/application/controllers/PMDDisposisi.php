<?php

use chillerlan\QRCode\QRCode;
use PhpOffice\PhpWord\TemplateProcessor;
use setasign\Fpdi\Fpdi;
use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property PMD_Disposition $pmddisposition
 * @property PMD_Leader $pmdleader
 * @property CI_Upload $upload
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property PMD_Member $pmdmember
 */
class PMDDisposisi extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('PMD_Disposition', 'pmddisposition');
        $this->load->model('PMD_Leader', 'pmdleader');
        $this->load->model('PMD_Member', 'pmdmember');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Disposisi';
        $data['content'] = 'pmd_disposisi/index';
        $data['disposition'] = $this->pmddisposition->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Permintaan Disposisi';
        $data['content'] = 'pmd_disposisi/add';
        $data['pmdmember'] = $this->pmdmember->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan permintaan disposisi');
        }

        $name = getPost('name');
        $document = $_FILES['document'];
        $date = getPost('date');
        $applicantname = getPost('applicantname');
        $refnumber = getPost('refnumber');
        // $pmdmemberid = getPost('pmdmemberid');
        // $coordinatex = getPost('coordinateX');
        // $coordinatey = getPost('coordinateY');

        try {
            $upload = doUpload_CloudStorage('document', 'pdf|doc|docx');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $insert = array();
        $insert['request'] = $name;
        $insert['requestdate'] = $date;
        $insert['applicantname'] = $applicantname;
        $insert['refnumber'] = $refnumber;
        $insert['document'] = $upload['name'];
        // $insert['pmdmemberid'] = $pmdmemberid;
        // $insert['coordinatey'] = round($coordinatey);
        // $insert['coordinatex'] = round($coordinatex);
        $insert['userid'] = getCurrentIdUser();
        $insert['status'] = 'Pending';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->pmddisposition->insert($insert);
        $dispositionid = $this->db->insert_id();

        $documenturl = asset_url($upload['name']);
        $url = base_url('pmd/approval?id=' . md5($dispositionid));

        $leader = $this->pmdleader->result();

        foreach ($leader as $key => $value) {
            $pesan = "Hallo, $value->name.

Anda mendapatkan permintaan disposisi baru terkait: $name

Dokumen terkait dapat diakses pada link berikut:
$documenturl

Harap segera melakukan konfirmasi pada link berikut:
$url";

            sendMessageWhatsApp($value->phonenumber, $pesan);
        }

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser(),
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/disposisi'));
        }

        $data = array();
        $data['title'] = 'Edit Permintaan Disposisi';
        $data['content'] = 'pmd_disposisi/edit';
        $data['disposition'] = $get->row();
        $data['pmdmember'] = $this->pmdmember->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan permintaan disposisi');
        }

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser(),
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $name = getPost('name');
        $document = $_FILES['document'];
        $date = getPost('date');
        $applicantname = getPost('applicantname');
        $refnumber = getPost('refnumber');
        // $pmdmemberid = getPost('pmdmemberid');
        // $coordinatex = getPost('coordinateX');
        // $coordinatey = getPost('coordinateY');

        $update = array();
        if (isset($document['size']) && $document['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('document', 'pdf|doc|docx');
                $update['document'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $update['request'] = $name;
        $update['requestdate'] = $date;
        $update['applicantname'] = $applicantname;
        $update['refnumber'] = $refnumber;
        // $update['pmdmemberid'] = $pmdmemberid;

        // if ($coordinatey != null && $coordinatex != null) {
        //     $update['coordinatey'] = round($coordinatey);
        //     $update['coordinatex'] = round($coordinatex);
        // }

        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->pmddisposition->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil diubah');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan permintaan disposisi');
        }

        $id = getPost('id');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser(),
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $this->pmddisposition->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil dihapus');
    }

    public function approval()
    {
        $id = getGet('id');

        $get = $this->pmddisposition->get(array(
            'MD5(id)' => $id,
        ));

        if ($get->num_rows() == 0) {
            return $this->load->view('errors/html/error_404', array(
                'heading' => 'Tidak Ditemukan',
                'message' => '<p>Permohonan Disposisi tidak ditemukan</p>'
            ));
        }

        $data = array();
        $data['title'] = 'Approval Permintaan Disposisi';
        $data['disposition'] = $get->row();

        return $this->load->view('pmd_disposisi/approval', $data);
    }

    public function set_approval()
    {
        $id = getPost('id');
        $status = getPost('status');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $update = array();
        $update['status'] = $status;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->pmddisposition->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil diubah');
    }

    public function barcode()
    {
        $id = getPost('id');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(
            array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('pmd_disposisi/barcode', array(
                    'disposition' => $row
                ), true)
            )
        );
    }

    public function process_barcode()
    {
        $id = getPost('id');
        // $suggestion = getPost('suggestion');
        $refnumberkadis = getPost('refnumberkadis');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $update = array();
        $update['status'] = 'Success';
        // $update['suggestion'] = $suggestion;
        $update['refnumberkadis'] = $refnumberkadis;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();
        $update['signtype'] = 'qr';

        $this->pmddisposition->update(array(
            'id' => $id
        ), $update);

        $member = $this->pmdmember->result();

        foreach ($member as $key => $value) {
            $message = 'Permintaan Disposisi dengan nomor ' . $get->row()->refnumber . ' telah disetujui oleh Kepala Dinas PMD pada ' . getCurrentDate('d F Y H:i:s') . "

Berikut adalah detail permintaan disposisi:
Nama Pemohon: " . $get->row()->applicantname . "
Tanggal Permohonan: " . tgl_indo($get->row()->requestdate) . "
Perihal Disposisi: " . $get->row()->request . "
Dokumen: " . asset_url($get->row()->document) . "

Dokumen Disposisi dari Kadis: " . base_url('pmd/disposisi/documentresult/' . $get->row()->id);

            sendMessageWhatsapp($value->phonenumber, $message);
        }

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil diubah');
    }

    public function document($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Success'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/disposisi'));
        }

        $row = $get->row();

        file_put_contents('./application/cache/' . $row->document, file_get_contents(asset_url($row->document)));

        $pdf = new Fpdi();
        $pdf->AddPage();
        $pdf->setSourceFile('./application/cache/' . $row->document);

        $tplId = $pdf->importPage(1);

        $pdf->useTemplate($tplId, 0, 5, 210);
        $pdf->SetFont('Arial', '', 10); // Font Name, Font Style (eg. 'B' for Bold), Font Size
        $pdf->SetTextColor(0, 0, 0); // RGB
        $pdf->SetXY($row->coordinatex / 1.11, $row->coordinatey + 0.9); // X start, Y start in mm
        $pdf->MultiCell(150, 5, $row->suggestion, 0, 'L', 0); // Text, Border (0 or 1), Text Align (L, R, C), Fill (0 or 1), Max Line Height (0 for unlimited)
        $pdf->Output();
    }

    public function digital()
    {
        $id = getPost('id');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(
            array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('pmd_disposisi/digital', array(
                    'disposition' => $row
                ), true)
            )
        );
    }

    public function process_digital()
    {
        $id = getPost('id');
        $data = getPost('data');
        // $suggestion = getPost('suggestion');
        $refnumberkadis = getPost('refnumberkadis');

        $get = $this->pmddisposition->get(array(
            'id' => $id,
            'status' => 'Pending'
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Permintaan disposisi tidak ditemukan');
        }

        $update = array();
        $update['status'] = 'Success';
        // $update['suggestion'] = $suggestion;
        $update['refnumberkadis'] = $refnumberkadis;
        $update['sign'] = $data;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();
        $update['signtype'] = 'digital';

        $this->pmddisposition->update(array(
            'id' => $id
        ), $update);

        $member = $this->pmdmember->result();

        foreach ($member as $key => $value) {
            $message = 'Permintaan Disposisi dengan nomor ' . $get->row()->refnumber . ' telah disetujui oleh Kepala Dinas PMD pada ' . getCurrentDate('d F Y H:i:s') . "

Berikut adalah detail permintaan disposisi:
Nama Pemohon: " . $get->row()->applicantname . "
Tanggal Permohonan: " . tgl_indo($get->row()->requestdate) . "
Perihal Disposisi: " . $get->row()->request . "
Dokumen: " . asset_url($get->row()->document) . "

Dokumen Disposisi dari Kadis: " . base_url('pmd/disposisi/documentresult/' . $get->row()->id);

            sendMessageWhatsapp($value->phonenumber, $message);
        }

        return JSONResponseDefault('OK', 'Permintaan disposisi berhasil diubah');
    }

    public function documentresult($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmddisposition->select('a.*')
            ->get(array(
                'a.id' => $id,
                'a.status' => 'Success'
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/disposisi'));
        }

        $row = $get->row();

        $template_path = './assets/template_surat/disposisi.docx';

        // Validate template file exists before processing
        if (!file_exists($template_path)) {
            error_log('Template file not found: ' . $template_path);
            return redirect(base_url('pmd/disposisi') . '?error=template_not_found');
        }

        try {
            $templateprocess = new TemplateProcessor($template_path);
        } catch (Exception $e) {
            error_log('Failed to load template: ' . $template_path . ' - Error: ' . $e->getMessage());
            return redirect(base_url('pmd/disposisi') . '?error=template_load_failed');
        }

        $templateprocess->setValues(array(
            '${refnumberkadis}' => $row->refnumberkadis,
            '${updateddate}' => tgl_indo(date('Y-m-d', strtotime($row->updateddate))) . ' ' . date('H:i:s', strtotime($row->updateddate)),
            '${applicantname}' => $row->applicantname,
            '${requestdate}' => tgl_indo($row->requestdate),
            '${refnumber}' => $row->refnumber,
            '${request}' => $row->request,
            // '${pmdmember}' => $row->pmdmembername,
        ));

        if ($row->signtype == 'qr') {
            $format = "Ditandatangani oleh Kepala Dinas pada tanggal $row->updateddate";
            $qrcode = (new QRCode())->render($format);

            if (preg_match('/^data:image\/(\w+);base64,/', $qrcode, $type)) {
                $data = substr($qrcode, strpos($qrcode, ',') + 1);
                $type = strtolower($type[1]);

                if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {
                    $data = str_replace(' ', '+', $data);
                    $data = base64_decode($data);

                    if ($data !== false) {
                        file_put_contents("./application/cache/sign_$row->refnumber." . $type, $data);

                        $templateprocess->setImageValue('ttd', array(
                            'path' => './application/cache/sign_' . $row->refnumber . '.' . $type,
                            'ratio' => true,
                            'width' => 300,
                        ));
                    }
                }
            }
        } else if ($row->signtype == 'digital') {
            if (preg_match('/^data:image\/(\w+);base64,/', $row->sign, $type)) {
                $data = substr($row->sign, strpos($row->sign, ',') + 1);
                $type = strtolower($type[1]);

                if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {
                    $data = str_replace(' ', '+', $data);
                    $data = base64_decode($data);

                    if ($data !== false) {
                        file_put_contents("./application/cache/sign_$row->refnumber." . $type, $data);

                        $templateprocess->setImageValue('ttd', array(
                            'path' => './application/cache/sign_' . $row->refnumber . '.' . $type,
                            'ratio' => true,
                            'width' => 100,
                        ));
                    }
                }
            }
        }

        $templateprocess->saveAs('./application/cache/' . 'DISPOSISI_' . $row->refnumberkadis . '.docx');

        try {
            $upload = doUpload_CloudStorage('./application/cache/' . 'DISPOSISI_' . $row->refnumberkadis . '.docx', 'docx', 'file');
            $filename = $upload['name'];
        } catch (Exception $ex) {
            return redirect(base_url('pmd/disposisi'));
        }

        $convert = convertWordToPDF($filename);

        if (isset($convert->success)) {
            $filename = explode('.', $filename);
            $filename = $filename[0] . '.pdf';
        }

        return redirect(asset_url($filename));
    }

    public function track($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmddisposition->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/disposisi'));
        }

        $data = array();
        $data['title'] = 'Tracking Disposisi';
        $data['content'] = 'pmd_disposisi/track';
        $data['disposisi'] = $get->row();

        return $this->load->view('master', $data);
    }
}
