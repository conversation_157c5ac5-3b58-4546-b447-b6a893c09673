<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Master_Users $masterusers
 * @property CI_Email $email
 * @property Operator_Desa $operatordesa
 */
class Auth extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Operator_Desa', 'operatordesa');
    }

    public function index()
    {
        if (isLogin()) {
            return redirect(base_url('admin'));
        }

        return $this->load->view('login_v2');
    }

    public function process_login()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda telah melakukan login');
        }

        $username = getPost('username');
        $password = getPost('password');
        $role = getPost('role');

        if ($username == null) {
            return JSONResponseDefault('FAILED', 'Username harus diisi');
        }

        if ($role == 'admin') {
            $get = $this->masterusers->get(array(
                'a.username' => $username,
                'a.password' => md5($password),
                "(LOWER(a.subdomain) = '" . $this->subdomain . "' OR a.subdomain IS NULL) =" => true,
                "(((a.platformname = '$this->platformname' OR a.platformname IS NULL) AND a.role = 'admin') OR (a.role != 'admin')) =" => true
            ));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Username atau password yang anda masukkan salah');
            } else {
                setSessionValue(array(
                    'ISLOGIN' => true,
                    'USERID' => $get->row()->id,
                    'USERNAME' => $username,
                    'ROLE' => $get->row()->role,
                    'ADMINTYPE' => $get->row()->admintype,
                    'PLATFORMNAME' => $get->row()->platformname
                ));

                return JSONResponseDefault('OK', 'Login berhasil');
            }
        } else {
            $get = $this->operatordesa->get(array(
                'a.username' => $username,
                'a.createdby' => $this->subdomain_account->id,
            ));

            if ($get->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Username atau password yang anda masukkan salah');
            } else {
                $row = $get->row();

                if (!password_verify($password, $row->password)) {
                    return JSONResponseDefault('FAILED', 'Username atau password yang anda masukkan salah');
                }

                setSessionValue(array(
                    'ISLOGIN' => true,
                    'USERID' => $row->id,
                    'USERNAME' => $username,
                    'ROLE' => 'Operator Desa',
                    'ADMINTYPE' => null
                ));

                return JSONResponseDefault('OK', 'Login berhasil');
            }
        }
    }

    public function logout()
    {
        destroySession();

        return redirect('auth/login');
    }

    public function request_resetpassword()
    {
        if (isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus logout terlebih dahulu');
        }

        $email = getPost('email');

        if ($email == null) {
            return JSONResponseDefault('FAILED', 'Email harus diisi');
        } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return JSONResponseDefault('FAILED', 'Format email yang anda masukkan salah');
        }

        $get = $this->masterusers->get(array('a.email' => $email));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Email yang anda masukkan tidak terdaftar');
        }

        $row = $get->row();

        $token = md5($row->username . $row->email . getCurrentDate());

        $this->masterusers->update(array(
            'id' => $row->id
        ), array(
            'resetpasswordtoken' => $token,
            'requestresetpassword' => getCurrentDate()
        ));

        $sendMail = sendMail('mail.karpeldevtech.com', '<EMAIL>', 'owr216he890', 465, $email, 'Go Digital Desa', 'Reset Password Akun Go Digital Desa', $this->load->view('profile/resetpassword', array(
            'token' => $token,
        ), true));

        if ($sendMail) {
            return JSONResponseDefault('OK', 'Email reset password berhasil dikirim');
        } else {
            return JSONResponseDefault('FAILED', 'Email reset password gagal dikirim');
        }
    }

    public function resetpassword()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard'));
        }

        $token = getGet('token');

        if ($token == null) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->masterusers->get(array('a.resetpasswordtoken' => $token));

        if ($get->num_rows() == 0) {
            return redirect(base_url('auth/login'));
        }

        $row = $get->row();
        $requestresetpassword = date('Y-m-d H:i:s', strtotime("$row->requestresetpassword +10 minutes"));

        if (getCurrentDate() > $requestresetpassword) {
            return redirect(base_url('auth/login'));
        }

        return $this->load->view('resetpassword', array(
            'token' => $token
        ));
    }

    public function process_resetpassword()
    {
        if (isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus logout terlebih dahulu');
        }

        $token = getPost('token');
        $newpassword = getPost('newpassword');
        $confirmpassword = getPost('confirmpassword');

        if ($token == null) {
            return JSONResponseDefault('FAILED', 'Token harus diisi');
        } else if ($newpassword == null) {
            return JSONResponseDefault('FAILED', 'Password baru harus diisi');
        } else if ($confirmpassword == null) {
            return JSONResponseDefault('FAILED', 'Konfirmasi password baru harus diisi');
        } else if ($newpassword != $confirmpassword) {
            return JSONResponseDefault('FAILED', 'Password baru dan konfirmasi password baru tidak sama');
        }

        $get = $this->masterusers->get(array('a.resetpasswordtoken' => $token));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Token yang anda masukkan salah');
        }

        $row = $get->row();

        $requestresetpassword = date('Y-m-d H:i:s', strtotime("$row->requestresetpassword +10 minutes"));

        if (getCurrentDate() > $requestresetpassword) {
            return JSONResponseDefault('FAILED', 'Token yang anda masukkan sudah kadaluarsa');
        }

        $this->masterusers->update(array(
            'id' => $row->id
        ), array(
            'password' => md5($newpassword),
            'resetpasswordtoken' => null,
            'requestresetpassword' => null
        ));

        return JSONResponseDefault('OK', 'Password berhasil diubah');
    }
}
