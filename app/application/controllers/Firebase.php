<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property FirebaseToken $firebasetoken
 * @property CI_DB_query_builder|CI_DB_mysqli_driver|CI_DB_mysql_driver $db
 */
class Firebase extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('FirebaseToken', 'firebasetoken');
    }

    public function save_token()
    {
        $token = getPost('token');

        $get = $this->firebasetoken->get(array(
            'token' => $token
        ));

        if ($get->num_rows() == 0) {
            $this->firebasetoken->insert(array(
                'userid' => getCurrentIdUser(),
                'token' => $token,
                'createddate' => getCurrentDate(),
                'createdby' => getCurrentIdUser(),
            ));
        } else {
            $this->firebasetoken->update(array(
                'userid' => getCurrentIdUser(),
                'token' => $token,
            ), array(
                'token' => $token,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser(),
            ));
        }

        return JSONResponseDefault('OK', 'Token berhasil disimpan');
    }

    public function sendNotification($title, $body, $deviceid)
    {
        $client = new Google_Client();
        $client->useApplicationDefaultCredentials();

        $client->setAuthConfig(APPPATH . 'config/firebase.json');

        $client->addScope('https://www.googleapis.com/auth/firebase.messaging');

        $httpClient = $client->authorize();

        $project = "gides-257fd";

        $message = [
            "message" => [
                "token" => $deviceid,
                "notification" => [
                    "body" => $body,
                    "title" => $title,
                ]
            ]
        ];

        $response = $httpClient->post("https://fcm.googleapis.com/v1/projects/$project/messages:send", [
            'json' => $message
        ]);

        print_r($response->getBody()->getContents());
    }
}
