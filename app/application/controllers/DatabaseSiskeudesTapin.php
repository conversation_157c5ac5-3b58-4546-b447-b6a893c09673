<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DatabaseSiskeudesTapin extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Database Siskeudes Tapin';
        $data['content'] = 'database_siskeudes_tapin/index';
        $data['table'] = getTablesSiskeudes();

        return $this->load->view('master', $data);
    }

    public function data()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Tidak ada data yang ditemukan');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('database_siskeudes_tapin/data', array(
                'name' => $name,
                'columns' => getColumnsSiskeudes($name),
            ), true)
        ));
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = getPost('name');

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Tidak ada data yang ditemukan');
        }

        $start = getPost('start');
        if ($start == 0) {
            $start = 1;
        } else {
            $start = $start / getPost('length') + 1;
        }

        $length = getPost('length');

        $data = getDataTables($name, $start, $length);
        $columns = getColumnsSiskeudes($name);

        $output = array();
        foreach ($data->data as $key => $value) {
            $detail = array();

            foreach ($columns->data as $k => $v) {
                $detail[] = $value->{$v};
            }

            $output[] = $detail;
        }

        return JSONResponse(array(
            'draw' => getPost('draw'),
            'recordsTotal' => $data->total,
            'recordsFiltered' => $data->total,
            'data' => $output,
        ));
    }
}
