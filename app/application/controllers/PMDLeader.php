<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property PMD_Leader $pmdleader
 */
class PMDLeader extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('PMD_Leader', 'pmdleader');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Pimpinan PMD';
        $data['content'] = 'pmd_leader/index';
        $data['leader'] = $this->pmdleader->result(array(
            'userid' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Pimpinan PMD';
        $data['content'] = 'pmd_leader/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan pimpinan PMD');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');

        $insert = array();
        $insert['name'] = $nama;
        $insert['phonenumber'] = $phonenumber;
        $insert['userid'] = getCurrentIdUser();
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['position'] = $jabatan;

        $this->pmdleader->insert($insert);

        return JSONResponseDefault('OK', 'Berhasil menambahkan pimpinan PMD');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->pmdleader->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('pmd/leader'));
        }

        $data = array();
        $data['title'] = 'Edit Pimpinan PMD';
        $data['content'] = 'pmd_leader/edit';
        $data['leader'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengubah pimpinan PMD');
        }

        $get = $this->pmdleader->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Pimpinan PMD tidak ditemukan');
        }

        $nama = getPost('nama');
        $jabatan = getPost('jabatan');
        $phonenumber = getPost('phonenumber');

        $update = array();
        $update['name'] = $nama;
        $update['phonenumber'] = $phonenumber;
        $update['position'] = $jabatan;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->pmdleader->update(array(
            'id' => $id
        ), $update);

        return JSONResponseDefault('OK', 'Berhasil mengubah pimpinan PMD');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Sesi telah berakhir, silahkan login kembali');
        } else if (!isPMD()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus pimpinan PMD');
        }

        $id = getPost('id');

        $get = $this->pmdleader->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Pimpinan PMD tidak ditemukan');
        }

        $this->pmdleader->delete(array(
            'id' => $id
        ));

        return JSONResponseDefault('OK', 'Berhasil menghapus pimpinan PMD');
    }
}
