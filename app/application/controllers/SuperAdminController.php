<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Master_Users $msusers
 * @property CI_Form_validation $form_validation
 */
class SuperAdminController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Master_Users', 'msusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Super Admin';
        $data['content'] = 'managesuperadmin/index';
        $data['superadmin'] = $this->msusers->result(array('role' => 'superadmin'));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Super Admin';
        $data['content'] = 'managesuperadmin/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $username = getPost('username');
        $password = getPost('password');
        $admintype = getPost('admintype');

        $this->form_validation->set_rules('username', 'Username', array('required', 'is_unique[msusers.username]'));
        $this->form_validation->set_rules('password', 'Password', array('required'));

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $insert = array();
        $insert['username'] = $username;
        $insert['password'] = md5($password);
        $insert['role'] = 'superadmin';
        $insert['platformname'] = $admintype;
        $insert['createddate'] = date('Y-m-d H:i:s');
        $insert['createdby'] = getCurrentIdUser();

        $this->msusers->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isSuperAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msusers->get(array('id' => $id, 'role' => 'superadmin'));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/superadmin'));
        }

        $data = array();
        $data['title'] = 'Edit Super Admin';
        $data['content'] = 'managesuperadmin/edit';
        $data['superadmin'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $get = $this->msusers->get(array('id' => $id, 'role' => 'superadmin'));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $username = getPost('username');
        $password = getPost('password');
        $admintype = getPost('admintype');

        $this->form_validation->set_rules('username', 'Username', array('required'));

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $validate_username = $this->msusers->get(array('username' => $username, 'id !=' => $id));

        if ($validate_username->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan');
        }

        $update = array();
        $update['username'] = $username;
        if ($password != null) {
            $update['password'] = md5($password);
        }
        $update['platformname'] = $admintype;
        $update['updateddate'] = date('Y-m-d H:i:s');
        $update['updatedby'] = getCurrentIdUser();

        $this->msusers->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Silahkan login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->msusers->get(array('id' => $id, 'role' => 'superadmin'));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->msusers->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}
