<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Kategori_Anggaran $kategori_anggaran
 * @property Detail_Kategori_Anggaran $detail_kategori_anggaran
 * @property Subdetail_Kategori_Anggaran $subdetail_kategori_anggaran
 * @property Warga_Model $warga
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 */
class CategoryBudgets extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Kategori_Anggaran', 'kategori_anggaran');
        $this->load->model('Detail_Kategori_Anggaran', 'detail_kategori_anggaran');
        $this->load->model('Subdetail_Kategori_Anggaran', 'subdetail_kategori_anggaran');
        $this->load->model('Warga_Model', 'warga');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Kategori Anggaran';
        $data['content'] = 'kategori_anggaran/index';
        $data['kategori'] = $this->kategori_anggaran->select('a.*, b.nama_kabkota')
            ->join('kabkota b', 'a.kabkotaid = b.id_kabkota', 'LEFT')
            ->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Tambah Kategori Anggaran';
        $data['content'] = 'kategori_anggaran/add';
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $kode = getPost('kode');
        $nama = getPost('nama');
        $kabupaten = getPost('kabupaten');
        $year = getPost('year');

        $data = array(
            'kode' => $kode,
            'nama' => $nama,
            'kabkotaid' => $kabupaten,
            'tahun' => $year
        );

        $insert = $this->kategori_anggaran->insert($data);

        if ($insert) {
            return JSONResponseDefault('OK', 'Berhasil menambahkan data');
        } else {
            return JSONResponseDefault('OK', 'Gagal menambahkan data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect('manage/category/budgets');
        }

        $data = array();
        $data['title'] = 'Edit Kategori Anggaran';
        $data['content'] = 'kategori_anggaran/edit';
        $data['kategori'] = $get->row();
        $data['kabkota'] = $this->warga->getKabkota(array('id_propinsi' => 22))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $kode = getPost('kode');
        $nama = getPost('nama');
        $kabupaten = getPost('kabupaten');
        $year = getPost('year');

        $data = array(
            'kode' => $kode,
            'nama' => $nama,
            'kabkotaid' => $kabupaten,
            'tahun' => $year
        );

        $update = $this->kategori_anggaran->update(array(
            'id' => $id
        ), $data);

        if ($update) {
            return JSONResponseDefault('OK', 'Berhasil mengubah data');
        } else {
            return JSONResponseDefault('OK', 'Gagal mengubah data');
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $id = getPost('id');

        $delete = $this->kategori_anggaran->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Berhasil menghapus data');
        } else {
            return JSONResponseDefault('OK', 'Gagal menghapus data');
        }
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect('manage/category/budgets');
        }

        $data = array();
        $data['title'] = 'Detail Kategori Anggaran';
        $data['content'] = 'kategori_anggaran/detail';
        $data['kategori'] = $get->row();
        $data['detail'] = $this->detail_kategori_anggaran->result(array(
            'kategorianggaranid' => $id
        ));

        return $this->load->view('master', $data);
    }

    public function process_add_detail($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $kode = getPost('kode');
        $nama = getPost('nama');

        $data = array(
            'kategorianggaranid' => $id,
            'kode' => $kode,
            'nama' => $nama
        );

        $insert = $this->detail_kategori_anggaran->insert($data);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan ditambahkan');
        }
    }

    public function process_delete_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->detail_kategori_anggaran->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_anggaran/edit_detail', array(
                'detail' => $row
            ), true)
        ));
    }

    public function process_edit_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $nama = getPost('nama');
        $kode = getPost('kode');

        $data = array(
            'kode' => $kode,
            'nama' => $nama,
        );

        $update = $this->detail_kategori_anggaran->update(array(
            'id' => $id
        ), $data);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function sub_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_anggaran/sub_detail', array(
                'detail' => $row,
                'sub_detail' => $this->subdetail_kategori_anggaran->get(array(
                    'detailkategorianggaranid' => $id
                ))->result()
            ), true)
        ));
    }

    public function process_add_sub_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->detail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $kode = getPost('kode');
        $activity = getPost('activity');
        $output = getPost('output');

        if ($output != null) {
            $output_explode = explode('[#]', $output ?? '');
            foreach ($output_explode as $key => $value) {
                $output_explode_split = explode('[-]', $value);

                if (count($output_explode_split) != 2) {
                    return JSONResponseDefault('FAILED', 'Format output salah');
                }
            }
        }

        $get = $this->subdetail_kategori_anggaran->get(array(
            'kode' => $kode,
            'detailkategorianggaranid' => $id
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kode sudah digunakan');
        }

        $data = array(
            'detailkategorianggaranid' => $id,
            'kode' => $kode,
            'activity' => $activity,
            'output' => $output
        );

        $insert = $this->subdetail_kategori_anggaran->insert($data);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan ditambahkan');
        }
    }

    public function process_delete_sub_detail($kategorianggaranid)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $get = $this->kategori_anggaran->get(array(
            'id' => $kategorianggaranid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $id = getPost('id');

        $get = $this->subdetail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->subdetail_kategori_anggaran->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function process_update_sub_detail()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $id = getPost('id');
        $kode = getPost('kode');
        $activity = getPost('activity');
        $output = getPost('output');

        $get = $this->subdetail_kategori_anggaran->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        if ($output != null) {
            $output_explode = explode('[#]', $output ?? '');
            foreach ($output_explode as $key => $value) {
                $output_explode_split = explode('[-]', $value);

                if (count($output_explode_split) != 2) {
                    return JSONResponseDefault('FAILED', 'Format output salah');
                }
            }
        }

        $data = array(
            'kode' => $kode,
            'activity' => $activity,
            'output' => $output
        );

        $update = $this->subdetail_kategori_anggaran->update(array(
            'id' => $id
        ), $data);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function importexcel()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('kategori_anggaran/importexcel', array(
                'kabupaten' => $this->warga->getKabkota(array('id_propinsi' => 22))->result()
            ), true)
        ));
    }

    public function process_importexcel()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda harus login terlebih dahulu');
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $kabupaten = getPost('kabupaten');
        $year = getPost('year');
        $file = $_FILES['file'];

        if (isset($file['name'])) {
            $file_tmp = $file['tmp_name'];

            $object = PHPExcel_IOFactory::load($file_tmp);
            $data = $object->getActiveSheet()->toArray();

            $this->db->trans_begin();

            $last_kategori_anggaran_id = null;
            $last_detail_kategori_anggaran_id = null;
            $last_subdetail_kategori_anggaran_id = null;
            foreach ($data as $key => $value) {
                if ($key == 0) continue;

                $kode = $value[0];
                $bidang = $value[1];
                $subbidang = $value[2];
                $kegiatan = $value[3];
                $kodeoutput = $value[4];
                $output = $value[5];

                $isbidang = false;
                $issubbidang = false;
                $iskegiatan = false;

                if ($kode != null && $bidang != null && $subbidang == null && $kegiatan == null && $kodeoutput == null && $output == null) {
                    $isbidang = true;
                } else if ($kode != null && $bidang == null && $subbidang != null && $kegiatan == null && $kodeoutput == null && $output == null) {
                    $issubbidang = true;
                } else if ($kode != null && $bidang == null && $subbidang == null && $kegiatan != null && $kodeoutput != null && $output != null) {
                    $iskegiatan = true;
                } else if ($kode == null && $bidang == null && $subbidang == null && $kegiatan == null && $kodeoutput != null && $output != null) {
                    $iskegiatan = true;
                }

                if ($isbidang) {
                    $insert = array();
                    $insert['nama'] = $bidang;
                    $insert['kode'] = $kode;
                    $insert['kabkotaid'] = $kabupaten;
                    $insert['tahun'] = $year;

                    $this->kategori_anggaran->insert($insert);
                    $last_kategori_anggaran_id = $this->db->insert_id();
                } else if ($issubbidang) {
                    $insert = array();
                    $insert['kategorianggaranid'] = $last_kategori_anggaran_id;
                    $insert['nama'] = $subbidang;
                    $insert['kode'] = $kode;

                    $this->detail_kategori_anggaran->insert($insert);
                    $last_detail_kategori_anggaran_id = $this->db->insert_id();
                } else if ($iskegiatan) {
                    if ($kode != null) {
                        $insert = array();
                        $insert['detailkategorianggaranid'] = $last_detail_kategori_anggaran_id;
                        $insert['activity'] = $kegiatan;
                        $insert['output'] = $kodeoutput . '[-]' . $output;
                        $insert['kode'] = $kode;

                        $this->subdetail_kategori_anggaran->insert($insert);
                        $last_subdetail_kategori_anggaran_id = $this->db->insert_id();
                    } else {
                        $get_subdetail_kategori_anggaran_id = $this->subdetail_kategori_anggaran->get(array(
                            'id' => $last_subdetail_kategori_anggaran_id
                        ));

                        if ($get_subdetail_kategori_anggaran_id->num_rows() > 0) {
                            $row_subdetail = $get_subdetail_kategori_anggaran_id->row();

                            $output_explode = explode('[#]', $row_subdetail->output);
                            $output_insert = $kodeoutput . '[-]' . $output;

                            $output_explode[] = $output_insert;

                            $output_implode = implode('[#]', $output_explode);

                            $update = array();
                            $update['output'] = $output_implode;

                            $this->subdetail_kategori_anggaran->update(array(
                                'id' => $last_subdetail_kategori_anggaran_id
                            ), $update);
                        }
                    }
                }
            }

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                return JSONResponseDefault('FAILED', 'Gagal mengimport data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengimport data');
        } else {
            return JSONResponseDefault('FAILED', 'File tidak ditemukan');
        }
    }

    public function format_excel()
    {
        $this->load->helper('download');
        return force_download('./assets/format_excel/Import Data APB.xlsx', null);
    }
}
