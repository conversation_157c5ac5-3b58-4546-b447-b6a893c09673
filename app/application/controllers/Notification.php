<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Notifications $notifications
 */
class Notification extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Notifications', 'notifications');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $read = getGet('read');

        if ($read != null) {
            $this->notifications->update(array(
                'id' => $read,
                'userid' => getCurrentIdUser()
            ), array(
                'is_read' => 1
            ));
        }

        $data = array();
        $data['title'] = 'Notifikasi';
        $data['content'] = 'notification';
        $data['notifications'] = $this->notifications->order_by('a.createddate', 'DESC')->result(array(
            'userid' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }
}
