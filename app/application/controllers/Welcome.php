<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 * @property Infografis_Model $infografis
 * @property Berita_Model $berita
 * @property Produk_Model $produk
 * @property Wisata_Model $wisata
 * @property Master_Users $masterusers
 * @property Slider_model $slider
 * @property Warga_Model $warga
 * @property Bidangpengaduans $bidangpengaduan
 * @property Arsip_Pengaduan $arsippengaduan
 * @property BPD_Notification $bpdnotification
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property CI_Upload $upload
 * @property GuestBook $guestbook
 * @property Bidang_Perangkat $bidangperangkat
 * @property Notifications $notifications
 */
class Welcome extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('Setting_Umum', 'settingumum');
		$this->load->model('Kontak_Penting', 'kontakpenting');
		$this->load->model('Infografis_Model', 'infografis');
		$this->load->model('Berita_Model', 'berita');
		$this->load->model('Produk_Model', 'produk');
		$this->load->model('Wisata_Model', 'wisata');
		$this->load->model('Master_Users', 'masterusers');
		$this->load->model('Slider_model', 'slider');
		$this->load->model('Warga_Model', 'warga');
		$this->load->model('Bidangpengaduans', 'bidangpengaduan');
		$this->load->model('Arsip_Pengaduan', 'arsippengaduan');
		$this->load->model('BPD_Notification', 'bpdnotification');
		$this->load->model('GuestBook', 'guestbook');
		$this->load->model('Bidang_Perangkat', 'bidangperangkat');
		$this->load->model('Notifications', 'notifications');
	}

	public function index()
	{
		$subdomainaccount_id = $this->subdomain_account->id;
		$subdomainaccount_kecamatan = $this->subdomain_account->kecamatanid;
		$data = array();

		$where = array(
			'a.id_user' => $this->subdomain_account->id
		);

		$where_infografis = array(
			"(a.id_user = '$subdomainaccount_id' OR (c.id_kecamatan = '$subdomainaccount_kecamatan' AND b.admintype = 'Kecamatan')) =" => true
		);

		$berita = $where;
		$berita['a.isverified'] = 1;

		$query_luas_tanah = "SELECT SUM(a.luas_tanah_kas) AS luas_tanah_kas, SUM(a.luas_tanah_desa) AS luas_tanah_desa, SUM(a.luas_dhkp) AS luas_dhkp FROM setting_umum a JOIN msusers b ON b.id = a.id_user LEFT JOIN kelurahan c ON c.id_kelurahan = b.kelurahanid WHERE (a.id_user = '$subdomainaccount_id' OR (c.id_kecamatan = '$subdomainaccount_kecamatan' AND b.admintype = 'Kecamatan'))";

		$setting = $this->settingumum->getDefaultData($where);
		$data['title'] = isset($setting->desa) ? 'Desa ' . $setting->desa : 'Go Digital Desa';
		$data['setting'] = $setting->row();
		$data['infografis'] = $this->infografis->select('SUM(a.laki) AS laki, SUM(a.perempuan) AS perempuan')
			->join('msusers b', 'b.id = a.id_user')
			->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
			->getDefaultData($where_infografis)
			->row();
		$data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
		$data['berita'] = $this->berita->select('a.*, b.nama AS kategori_berita')
			->join('kategori_berita b', 'b.id = a.categoryid', 'LEFT')
			->limit(9)
			->order_by('a.createddate', 'DESC')
			->get($berita)
			->result();

		$data['produk'] = $this->produk->limit(9)->getDefaultData($where)->result();
		$data['kategori_produk'] = $this->produk->select('kategori')->group_by('kategori')->getDefaultData($where)->result();
		$data['wisata'] = $this->wisata->getDefaultData($where)->result();
		$data['slider'] = $this->slider->getDefaultData($where)->result();
		if ($setting->row()->slider_news == 1) {
			$data['slider_berita'] = $this->berita->limit(3)->order_by('a.createddate', 'DESC')->result($berita);
		} else {
			$data['slider_berita'] = array();
		}
		$data['bidang'] = $this->bidangpengaduan->result(array(
			'userid' => $this->subdomain_account->id
		));
		$data['user'] = $this->subdomain_account;
		$data['luas_tanah'] = $this->db->query($query_luas_tanah)->row();
		$data['content'] = 'landing/index';

		$border = $this->db->query("select a.username, b.nama_kelurahan, c.nama_kecamatan, d.nama_kabkota, e.border, e.custom_border from msusers a left join kelurahan b on b.id_kelurahan = a.kelurahanid left join kecamatan c ON c.id_kecamatan = b.id_kecamatan left join kabkota d on d.id_kabkota = c.id_kabkota left join villagesborder e on e.village = b.nama_kelurahan and e.sub_district = c.nama_kecamatan where a.id = '" . $this->subdomain_account->id . "' order by c.nama_kecamatan asc")->row();

		if ($border->border != null || $border->custom_border != null) {
			if ($border->custom_border != null) {
				$border_decoded = json_decode($border->custom_border);
			} else {
				$border_decoded = json_decode($border->border);
			}

			$coordinates = array();
			foreach ($border_decoded as $key => $value) {
				$coordinates[] = array(
					'lat' => (float)$value[1],
					'lng' => (float)$value[0]
				);
			}

			$data['coords'] = json_encode($coordinates);
			if (isset($coordinates[0]['lat']) && isset($coordinates[0]['lng']) && $coordinates[0]['lat'] != null && $coordinates[0]['lng'] != null) {
				$data['firstcoords'] = json_encode(array(
					'lat' => $coordinates[0]['lat'] - 0.02,
					'lng' => $coordinates[0]['lng'] + 0.03
				));
				// $data['firstcoords'] = null;
			} else {
				$data['firstcoords'] = null;
			}
		} else {
			$data['coords'] = null;
		}

		if ($setting->num_rows() > 0) {
			// ProGides platform always uses ProGides theme regardless of theme setting
			if (getPlatformName() == 'ProGides') {
				$data['content'] = 'progides/index';

				return $this->load->view('progides/master', $data);
			} elseif ($this->subdomain_account->themeid == 2) {
				return $this->load->view('profile_v2/index', $data);
			} else {
				return $this->load->view('landing/master', $data);
			}
		} else {
			return $this->load->view('profile_v2/configuration', $data);
		}
	}

	public function process_kritiksaran()
	{
		if (!isPostAjax()) {
			return JSONResponseRequestReject();
		}

		$nik = getPost('nik');
		$kritiksaran = getPost('pesan');
		$foto = $_FILES['foto'];
		$bidangpengaduan = getPost('bidangpengaduan');
		$lampiran = null;

		if (isset($foto['size']) && $foto['size'] > 0) {
			try {
				$upload = doUpload_CloudStorage('foto', 'jpg|jpeg|png');
				$file_name = $upload['name'];

				$url = asset_url($file_name);
				$lampiran = "Lampiran Pendukung
$url";
			} catch (Exception $ex) {
				return JSONResponseDefault('FAILED', $ex->getMessage());
			}
		}

		$get = $this->warga->get(array('nik' => $nik, 'id_user' => $this->subdomain_account->id));

		if ($get->num_rows() == 0) {
			return JSONResponseDefault('FAILED', 'NIK Yang anda masukkan tidak terdaftar');
		}

		$row_warga = $get->row();

		$setting = $this->settingumum->getDefaultData(array(
			'a.id_user' => $this->subdomain_account->id
		))->row();

		$kontak = $this->kontakpenting->getDefaultData(array(
			'a.has_notification' => 1,
			'a.id_user' => $this->subdomain_account->id
		));

		$bpd = $this->bpdnotification->result(array(
			'userid' => $this->subdomain_account->id
		));

		foreach ($kontak->result() as $key => $value) {
			$pesan = "Aspirasi Masyarakat $setting->desa\n\n$kritiksaran\n\n\nInformasi dari\n$row_warga->nama\n$row_warga->alamat, $row_warga->rt, $row_warga->rw\n\n$lampiran";
			sendMessageWhatsapp($value->no_telp, $pesan);
		}

		foreach ($bpd as $key => $value) {
			$pesan = "Aspirasi Masyarakat $setting->desa\n\n$kritiksaran\n\n\nInformasi dari\n$row_warga->nama\n$row_warga->alamat, $row_warga->rt, $row_warga->rw\n\n$lampiran";
			sendMessageWhatsapp($value->phonenumber, $pesan);
		}

		$insert = array();
		$insert['wargaid'] = $row_warga->nik;
		$insert['isi'] = $kritiksaran;

		if (isset($file_name)) {
			$insert['dokumen'] = $file_name;
		}

		$insert['tanggalpengaduan'] = getCurrentDate();
		$insert['bidangpengaduanid'] = $bidangpengaduan;
		$insert['userid'] = $this->subdomain_account->id;

		$this->arsippengaduan->insert($insert);

		$insert = array();
		$insert['userid'] = $this->subdomain_account->id;
		$insert['message'] = "Ada aspirasi dari warga\n\n$kritiksaran\n\nInformasi dari\n$row_warga->nama\n$row_warga->alamat, $row_warga->rt, $row_warga->rw";
		$insert['link'] = base_url('arsip/pengaduan');
		$insert['createddate'] = getCurrentDate();

		$this->notifications->insert($insert);

		return JSONResponseDefault('OK', 'Kritik dan Saran berhasil dikirim');
	}

	public function pengaduan()
	{
		$data = array();

		$where = array(
			'a.id_user' => $this->subdomain_account->id
		);

		$setting = $this->settingumum->getDefaultData($where);
		$data['title'] = 'Pengaduan Masyarakat';
		$data['setting'] = $setting->row();
		$data['kontakpenting'] = $this->kontakpenting->getDefaultData($where)->result();
		$data['content'] = 'profile/pengaduan';
		$data['bidang'] = $this->bidangpengaduan->result(array(
			'userid' => $this->subdomain_account->id
		));

		if ($setting->num_rows() > 0) {
			return $this->load->view('profile/master', $data);
		} else {
			return $this->load->view('profile_v2/configuration', $data);
		}
	}

	public function guestbook()
	{
		$data = array();

		$where = array(
			'a.id_user' => $this->subdomain_account->id
		);

		$setting = $this->settingumum->getDefaultData($where);
		$data['title'] = 'Buku Tamu';
		$data['setting'] = $setting->row();
		$data['content'] = 'profile/guestbook';
		$data['bidang'] = $this->bidangperangkat->result(array(
			'createdby' => $this->subdomain_account->id
		));

		if ($setting->num_rows() > 0) {
			// ProGides platform always uses ProGides theme regardless of theme setting
			if (getPlatformName() == 'ProGides') {
				$data['content'] = 'progides/guestbook';
				return $this->load->view('progides/master', $data);
			} else {
				return $this->load->view('profile/guestbook', $data);
			}
		} else {
			return $this->load->view('profile_v2/configuration', $data);
		}
	}

	public function process_guestbook()
	{
		$name = getPost('name');
		$phonenumber = getPost('phonenumber');
		$instance = getPost('instance');
		$address = getPost('address');
		$keperluan = getPost('keperluan');
		$bidang = getPost('bidang');
		$base64_foto = getPost('base64_foto');
		$foto = $_FILES['foto'];
		$appointment = getPost('appointment');
		$date_appointment = getPost('date_appointment');
		$time_appointment = getPost('time_appointment');

		$insert = array();
		$insert['userid'] = $this->subdomain_account->id;
		$insert['name'] = $name;
		$insert['phonenumber'] = $phonenumber;
		$insert['instance'] = $instance;
		$insert['address'] = $address;
		$insert['keperluan'] = $keperluan;
		$insert['bidangid'] = $bidang;
		$insert['createddate'] = getCurrentDate();

		if ($appointment != null) {
			$insert['appointment'] = $appointment;
			$insert['appointmentdate'] = date('Y-m-d H:i:s', strtotime("$date_appointment $time_appointment"));
		}

		if (isset($foto['size']) && $foto['size'] > 0) {
			try {
				$upload = doUpload_CloudStorage('foto', 'jpg|jpeg|png');
				$file_name = $upload['name'];

				$insert['foto'] = $file_name;
			} catch (Exception $ex) {
				return JSONResponseDefault('FAILED', $ex->getMessage());
			}
		} else if ($base64_foto != null) {
			base64_to_jpeg($base64_foto, './application/cache/' . md5($base64_foto) . '.jpg');

			try {
				$upload = doUpload_CloudStorage('./application/cache/' . md5($base64_foto) . '.jpg', 'jpg|jpeg', 'file');
				$filename = $upload['name'];
			} catch (Exception $ex) {
				return JSONResponseDefault('FAILED', $ex->getMessage());
			}

			$insert['foto'] = $filename;
		}

		$this->guestbook->insert($insert);
		$guestbookid = $this->db->insert_id();

		$bidangperangkat = $this->bidangperangkat->get(array('id' => $bidang))->row();
		if ($appointment && $bidangperangkat) {
			$date = date('d F Y H:i:s', strtotime("$date_appointment $time_appointment"));
			$message = "Ada tamu yang ingin melakukan Janji Temu dengan $bidangperangkat->name pada $date\n\nNama: $name\nNo. HP: $phonenumber\nInstansi: $instance\nAlamat: $address\nKeperluan: $keperluan\n\nSilahkan masuk ke halaman dibawah ini untuk menerima atau menolak janji temu\n\n" . base_url('guestbook/appointment/' . $guestbookid);
			sendMessageWhatsapp($bidangperangkat->phonenumber, $message);
		}

		$insert = array();
		$insert['userid'] = $this->subdomain_account->id;
		if ($appointment && $bidangperangkat) {
			$date = date('d F Y H:i:s', strtotime("$date_appointment $time_appointment"));
			$insert['message'] = "Ada tamu yang ingin melakukan Janji Temu dengan $bidangperangkat->name pada $date\n\nNama: $name\nNo. HP: $phonenumber\nInstansi: $instance\nAlamat: $address\nKeperluan: $keperluan";
		} else {
			$insert['message'] = "Ada tamu yang ingin bertamu\n\nNama: $name\nNo. HP: $phonenumber\nInstansi: $instance\nAlamat: $address\nKeperluan: $keperluan";
		}
		$insert['link'] = base_url('guestbook/appointment/' . $guestbookid);
		$insert['createddate'] = getCurrentDate();

		$this->notifications->insert($insert);

		return JSONResponseDefault('OK', 'Buku Tamu berhasil dikirim');
	}

	public function appointment($id)
	{
		$read = getGet('read');

		if ($read != null) {
			$this->notifications->update(array(
				'id' => $read,
				'userid' => $this->subdomain_account->id
			), array(
				'is_read' => 1
			));
		}

		$get_setting = $this->settingumum->getDefaultData(array('a.id_user' => $this->subdomain_account->id))->row();

		$get = $this->guestbook->select('a.*, b.name AS bidangname')
			->join('bidangperangkat b', 'b.id = a.bidangid')
			->get(array(
				'a.id' => $id,
				'a.userid' => $this->subdomain_account->id,
			));

		if ($get->num_rows() == 0) {
			return redirect(base_url('guestbook'));
		}

		$row = $get->row();

		$data = array();
		$data['title'] = 'Janji Temu';
		$data['guestbook'] = $row;
		$data['logo'] = $get_setting->logo_desa;

		return $this->load->view('profile/appointment', $data);
	}
}
