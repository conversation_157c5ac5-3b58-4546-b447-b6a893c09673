<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Slider_model $slider
 * @property Datatables $datatables
 */
class Slider extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Slider_model', 'slider');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = "Setting Slider";
        $data['content'] = 'slider/index';
        $data['slider'] = $this->slider->getDefaultData(array('id_user' => getCurrentIdUser()))->result();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Slider_model', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('a.id_user' => getCurrentIdUser())) as $key => $value) {
            if ($value->image_slider != null) {
                $foto = "<img src=\"" . asset_url($value->image_slider) . "\" width=\"100\">";
            } else {
                if ($value->video_slider != null) {
                    $foto = "<a href=\"" . $value->video_slider . "\" target=\"_blank\">Lihat Video</a>";
                } else {
                $foto = "- Foto Tidak Ditemukan -";
            }
            }

            $detail = array();
            $detail[] = $foto;
            $detail[] = "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"delete_slider($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Setting Slider - Add';
        $data['content'] = 'slider/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $slidertype = getPost('slidertype');
        $slider_url = getPost('slider_url');
        $slider = $_FILES['slider'];

        if ($slidertype == 'Image') {
        if ($slider['size'] == 0) {
            return JSONResponseDefault('FAILED', 'Slider required');
        }
        } else {
            if ($slider_url == null) {
                return JSONResponseDefault('FAILED', 'Slider URL required');
            }
        }

        try {
            if ($slidertype == 'Image') {
            $upload = doUpload_CloudStorage('slider', 'jpg|jpeg|png');
            }

            $insert = array();
            if ($slidertype == 'Image') {
            $insert['image_slider'] = $upload['name'];
            } else {
                $insert['video_slider'] = $slider_url;
            }
            $insert['slidertype'] = $slidertype;
            $insert['id_user'] = getCurrentIdUser();

            $insert = $this->slider->insert($insert);

            if ($insert) {
                return JSONResponseDefault('OK', 'Slider berhasil ditambahkan');
            } else {
                return JSONResponseDefault('FAILED', 'Gagal menambahkan slider');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->slider->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data not found');
        }

        $delete = $this->slider->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }
}
