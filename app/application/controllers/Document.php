<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CI_Upload $upload
 */
class Document extends MY_Controller
{
    public function api_convertdocxtopdf()
    {
        try {
            $upload = doUpload_CloudStorage('document', 'docx');
            $filename = $upload['name'];
            $remove_ext = explode('.', $filename);

            $convert = convertWordToPDF($filename);

            if ($convert && !isset($convert->error)) {
                return JSONResponse(array(
                    'status' => 'success',
                    'message' => 'File berhasil dikonversi',
                    'data' => array(
                        'filename' => asset_url($remove_ext[0] . '.pdf')
                    )
                ));
            } else {
                return JSONResponse(array(
                    'status' => 'error',
                    'message' => 'File gagal dikonversi'
                ));
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
