<?php
defined('BASEPATH') or exit('No direct script access allowed');

class BidangPengaduan extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Bidang Pengaduan';
        $data['content'] = 'bidang_pengaduan/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Bidangpengaduans', 'QueryDatatables', 'SearchDatatables');

        $data = array();
        foreach ($datatables->getData(array('userid' => getCurrentIdUser())) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $detail[] = "<a href=\"" . base_url('master/bidangpengaduan/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1\">
                <i class=\"fa fa-edit\"></i>
            </a>
            
            <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteData('$value->name', '$value->id')\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }
}
