<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Kategori_Berita $kategoriberita
 * @property CI_Upload $upload
 * @property Berita_Model $berita
 * @property Master_Users $masterusers
 * @property Notifikasi_Berita $notifikasiberita
 * @property Setting_Umum $settingumum
 * @property Kontak_Penting $kontakpenting
 * @property VisitorBerita $visitorberita
 * @property CI_Pagination $pagination
 * @property Datatables $datatables
 * @property Config_Prompt $config_prompt
 */
class Berita extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Berita_Model', 'berita');
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Kontak_Penting', 'kontakpenting');
        $this->load->model('Master_Users', 'masterusers');
        $this->load->model('Kategori_Berita', 'kategoriberita');
        $this->load->model('VisitorBerita', 'visitorberita');
        $this->load->model('Notifikasi_Berita', 'notifikasiberita');
        $this->load->model('Config_Prompt', 'config_prompt');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Berita';
        $data['content'] = 'berita/index';
        $data['status'] = null;

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Berita - Add';
        $data['content'] = 'berita/add';
        $data['kategori_berita'] = $this->kategoriberita->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $judul = getPost('judul');
        $deskripsi = getPost('deskripsi');
        $keywords = getPost('keywords');
        $categoryid = getPost('categoryid');

        if ($judul == null) {
            return JSONResponseDefault('FAILED', 'Judul harus diisi');
        } else if ($deskripsi == null) {
            return JSONResponseDefault('FAILED', 'Deskripsi harus diisi');
        } else {
            $wordcount = getWordCount($deskripsi);

            if ($wordcount < 100) {
                return JSONResponseDefault('FAILED', 'Deskripsi minimal 100 kata');
            }
        }

        $insert = array();
        try {
            $upload = doUpload_CloudStorage('foto');
            $insert['foto'] = $upload['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $insert['judul'] = $judul;
        $insert['link_access'] = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $judul)));
        $insert['tanggal'] = getCurrentDate();
        $insert['deskripsi'] = $deskripsi;
        $insert['keywords'] = $keywords;
        $insert['categoryid'] = $categoryid;
        $insert['id_user'] = getCurrentIdUser();
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();
        $insert['isverified'] = 1;

        $doInsert = $this->berita->insert($insert);

        if ($doInsert) {
            return JSONResponseDefault('OK', 'Berita berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menambahkan berita');
        }
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        }

        $id = getPost('id');

        $get = $this->berita->getDefaultData(array(
            'a.id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->berita->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array(
            'a.id' => $id,
        );

        if (!isSuperAdmin()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $get = $this->berita->getDefaultData($where);

        if ($get->num_rows() == 0) {
            return redirect('berita');
        }

        $data = array();
        $data['title'] = 'Berita - Edit';
        $data['content'] = 'berita/edit';
        $data['berita'] = $get->row();
        $data['kategori_berita'] = $this->kategoriberita->result();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        }

        $where = array(
            'a.id' => $id
        );

        if (!isSuperAdmin()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $get = $this->berita->getDefaultData($where);

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $judul = getPost('judul');
        $deskripsi = getPost('deskripsi');
        $keywords = getPost('keywords');
        $categoryid = getPost('categoryid');

        if ($judul == null) {
            return JSONResponseDefault('FAILED', 'Judul harus diisi');
        } else if ($deskripsi == null) {
            return JSONResponseDefault('FAILED', 'Deskripsi harus diisi');
        } else {
            $wordcount = getWordCount(extract_text($deskripsi));

            if ($wordcount < 100) {
                return JSONResponseDefault('FAILED', 'Deskripsi minimal 100 kata');
            }
        }

        $update = array();
        if (isset($_FILES['foto']) && $_FILES['foto']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('foto');
                $update['foto'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $update['judul'] = $judul;
        $update['deskripsi'] = $deskripsi;
        $update['keywords'] = $keywords;
        $update['categoryid'] = $categoryid;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $doUpdate = $this->berita->update(array(
            'id' => $id
        ), $update);

        if ($doUpdate) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengubah data');
        }
    }

    public function view_berita($tahun, $link_access)
    {
        $id = getGet('id');
        $embed = getGet('embed');
        $subdomainid = $this->subdomain_account->id;

        if ($id != null) {
            $get = $this->berita->select('a.*, b.nama AS nama_kategori')
                ->join('kategori_berita b', 'b.id = a.categoryid', 'LEFT')
                ->get(array(
                    'YEAR(a.createddate) =' => $tahun,
                    'a.id_user' => $subdomainid,
                    'a.id' => $id,
                    'a.isverified' => 1
                ));
        } else {
            $get = $this->berita->select('a.*, b.nama AS nama_kategori')
                ->join('kategori_berita b', 'b.id = a.categoryid', 'LEFT')
                ->get(array(
                    'YEAR(a.createddate) =' => $tahun,
                    'a.id_user' => $subdomainid,
                    'a.isverified' => 1
                ));
        }

        if ($get->num_rows() == 0) {
            return redirect(base_url());
        }

        $where = array(
            'a.id_user' => $subdomainid
        );

        $setting = $this->settingumum->get($where);

        $data = array();
        $data['berita'] = $get->row();
        $data['title'] = $data['berita']->judul;
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->result($where);
        $data['other'] = $this->berita->result(array(
            'a.id !=' => $get->row()->id,
            'a.id_user' => $subdomainid
        ));

        $visitor = $this->visitorberita->get(array(
            'ipaddress' => get_client_ip(),
            'DATE(createddate) =' => getCurrentDate('Y-m-d')
        ));

        if ($setting->num_rows() > 0) {
            if ($visitor->num_rows() == 0) {
                $this->visitorberita->insert(array(
                    'beritaid' => $id,
                    'useragent' => $_SERVER['HTTP_USER_AGENT'],
                    'ipaddress' => get_client_ip(),
                    'createddate' => getCurrentDate()
                ));
            }

            $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;

            if ($embed == 'share' && $referer == base_url('berita')) {
                return $this->load->view('profile/share_berita', $data);
            } else {
                // ProGides platform always uses ProGides theme regardless of theme setting
                if (getPlatformName() == 'ProGides') {
                    $data['content'] = 'progides/detail_berita';
                    return $this->load->view('progides/master', $data);
                } elseif ($this->subdomain_account->themeid == 2) {
                    $data['content'] = 'profile/berita';
                    return $this->load->view('profile/master', $data);
                } else {
                    $data['content'] = 'landing/detail_berita';
                    return $this->load->view('landing/master', $data);
                }
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function view_all($seg = 0)
    {
        $category = getGet('category');
        $q = getGet('q');
        $subdomainid = $this->subdomain_account->id;

        $where = array(
            'a.id_user' => $subdomainid,
        );
        $where_berita = array();

        if ($this->subdomain_account->admintype != 'Kecamatan') {
            $where_berita['a.id_user'] = $subdomainid;
        } else {
            $subdomainaccount_kecamatan = $this->subdomain_account->kecamatanid;
            $where_berita["(a.id_user = '$subdomainid' OR c.id_kecamatan = '$subdomainaccount_kecamatan') ="] = true;
        }

        $this->load->library('pagination');

        $config = array();
        $config['base_url'] = base_url('berita/all');
        $config['total_rows'] = $this->berita->join('msusers b', 'b.id = a.id_user')->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')->total($where_berita);
        $config['per_page'] = 6;
        $choice = $config["total_rows"] / $config["per_page"];
        $config["num_links"] = floor($choice);
        $config['first_link'] = 'First';
        $config['last_link'] = 'Last';
        if ($this->subdomain_account->themeid == 2) {
            $config['next_link'] = 'Next';
            $config['prev_link'] = 'Prev';
            $config['full_tag_open'] = '<div class="pagging text-center"><nav><ul class="pagination justify-content-center">';
        } else {
            $config['full_tag_open'] = '<div class="pagging text-center"><nav class="mt-2"><ul class="pagination justify-content-end">';
        }
        $config['full_tag_close'] = '</ul></nav></div>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '<span class="sr-only">(current)</span></a></li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tagl_close'] = '<span aria-hidden="true">&raquo;</span></li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tagl_close'] = 'Next</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tagl_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tagl_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');

        $this->pagination->initialize($config);

        $where_berita['a.isverified'] = 1;

        if ($category != null) {
            $where_berita['a.categoryid'] = $category;
        }

        $setting = $this->settingumum->get($where);

        $data = array();
        $data['title'] = 'Berita';
        $data['setting'] = $setting->row();
        $data['kontakpenting'] = $this->kontakpenting->result($where);
        $data['page'] = $seg;
        if ($this->subdomain_account->themeid == 2) {
            $data['berita'] = $this->berita->select('a.*')
                ->join('msusers b', 'b.id = a.id_user')
                ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                ->order_by('a.createddate', 'DESC')
                ->limit($config["per_page"], $data['page'])
                ->result($where_berita);
        } else {
            $this->berita->select('a.*, d.nama AS kategori_berita')
                ->join('msusers b', 'b.id = a.id_user')
                ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid', 'LEFT')
                ->join('kategori_berita d', 'd.id = a.categoryid', 'LEFT')
                ->order_by('a.createddate', 'DESC')
                ->limit($config["per_page"], $data['page']);

            if ($q != null) {
                $this->berita->like('a.judul', $q);
            }

            $data['berita'] = $this->berita->result($where_berita);
        }

        $data['pagination'] = $this->pagination->create_links();
        $data['kategori'] = $this->kategoriberita->result();

        if ($this->subdomain_account->themeid == 2) {
            $data['kategori'] = $this->kategoriberita->limit(4)->result();
            $data['kategorilain'] = $this->kategoriberita->limit(4, 4)->result();
        } else {
            $data['category'] = $category;
            $data['q'] = $q;
        }

        if ($setting->num_rows() > 0) {
            // ProGides platform always uses ProGides theme regardless of theme setting
            if (getPlatformName() == 'ProGides') {
                $data['content'] = 'progides/berita';
                return $this->load->view('progides/master', $data);
            } elseif ($this->subdomain_account->themeid == 2) {
                $data['content'] = 'profile/all_berita';
                return $this->load->view('profile/master', $data);
            } else {
                $data['content'] = 'landing/berita';
                return $this->load->view('landing/master', $data);
            }
        } else {
            return $this->load->view('profile_v2/configuration', $data);
        }
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        } else if (!isAdmin() && !isKecamatan_withWeb()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatables = $this->datatables->make('Berita_Model', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin() || isKecamatan_withWeb()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        foreach ($datatables->getData($where) as $key => $value) {
            if ($value->isverified == null) {
                $isverified = "<label class=\"badge badge-warning\">Menunggu Konfirmasi Admin</label>";
            } elseif ($value->isverified == 1) {
                $isverified = "<label class=\"badge badge-success\">Diterbitkan</label>";
            } elseif ($value->isverified == 2) {
                $isverified = "<label class=\"badge badge-danger\">Ditolak</label>";
            }

            if ($value->foto != null) {
                $foto = "<a href=\"" . asset_url($value->foto) . "\">Preview</a>";
            } else {
                $foto = "-";
            }

            // $actions = "<button type=\"button\" class=\"btn btn-warning btn-sm mb-1\" onclick=\"shareBerita($value->id)\">
            //     <i class=\"fa fa-share-alt\"></i> Bagikan
            // </button>

            // <a href=\"" . base_url('berita/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1\">
            //     <i class=\"fa fa-edit\"></i>
            // </a>

            // <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteBerita($value->id)\">
            //     <i class=\"fa fa-trash\"></i>
            // </button>";

            $actions = "<a href=\"" . base_url('berita/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1\">
                <i class=\"fa fa-edit\"></i>
            </a>

            <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteBerita($value->id)\">
                <i class=\"fa fa-trash\"></i>
            </button>";

            $detail = array();
            $detail[] = DateFormat($value->tanggal, 'd F Y H:i:s');
            $detail[] = $isverified;
            $detail[] = IDR($value->total) . ' Pengunjung';
            $detail[] = IDR($value->sharescount) . ' Share';
            $detail[] = $value->judul;
            $detail[] = $value->nama_kategori ?? 'Kabar Berita';
            $detail[] = $foto;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function generate_description()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        }

        $title = getPost('title');
        $location = getPost('location');
        $target = getPost('target');
        $date = getPost('date');
        $time = getPost('time');
        $iscovid = getPost('iscovid');
        $description = getPost('description');

        if ($title == null) {
            return JSONResponseDefault('FAILED', 'Judul tidak boleh kosong');
        } else if ($location == null) {
            return JSONResponseDefault('FAILED', 'Lokasi tidak boleh kosong');
        } else if ($target == null) {
            return JSONResponseDefault('FAILED', 'Tujuan tidak boleh kosong');
        } else if ($date == null) {
            return JSONResponseDefault('FAILED', 'Tanggal tidak boleh kosong');
        } else if ($time == null) {
            return JSONResponseDefault('FAILED', 'Waktu tidak boleh kosong');
        }

        $prompt = $this->config_prompt->get()->row();

        if ($prompt == null) {
            return JSONResponseDefault('FAILED', 'Prompt tidak ditemukan');
        }

        $description_news = $prompt->description_news;
        $description_news = str_replace('${title}', $title, $description_news);
        $description_news = str_replace('${location}', $location, $description_news);
        $description_news = str_replace('${target}', $target, $description_news);
        $description_news = str_replace('${date}', $date, $description_news);
        $description_news = str_replace('${time}', $time, $description_news);
        $description_news = str_replace('${description}', $description, $description_news);

        $description_covid_news = $prompt->description_covid_news;
        $description_covid_news = str_replace('${title}', $title, $description_covid_news);
        $description_covid_news = str_replace('${location}', $location, $description_covid_news);
        $description_covid_news = str_replace('${target}', $target, $description_covid_news);
        $description_covid_news = str_replace('${date}', $date, $description_covid_news);
        $description_covid_news = str_replace('${time}', $time, $description_covid_news);
        $description_covid_news = str_replace('${description}', $description, $description_covid_news);

        if ($iscovid) {
            $generateOpenAI = generateOpenAI($description_covid_news);
        } else {
            $generateOpenAI = generateOpenAI($description_news);
        }

        if (isset($generateOpenAI->result) && $generateOpenAI->result) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'DESCRIPTION' => $generateOpenAI->message,
                'NOT_ANSWER' => is_openai_not_answer($generateOpenAI->message),
            ));
        } else {
            log_message('ERROR', json_encode($generateOpenAI));

            return JSONResponseDefault('FAILED', 'Gagal mengambil data');
        }
    }

    public function fixtitle()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login');
        }

        $title = getPost('title');

        if ($title == null) {
            return JSONResponseDefault('FAILED', 'Judul tidak boleh kosong');
        }

        $prompt = $this->config_prompt->get()->row();

        if ($prompt == null) {
            return JSONResponseDefault('FAILED', 'Prompt tidak ditemukan');
        }

        $title_news = $prompt->title_news;
        $title_news = str_replace('${title}', $title, $title_news);

        $generateOpenAI = generateOpenAI($title_news);

        if (isset($generateOpenAI->result) && $generateOpenAI->result) {
            $response = $generateOpenAI->message;
            $response = substr($response, 1, -1);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'TITLE' => $response,
                'NOT_ANSWER' => is_openai_not_answer($generateOpenAI->message),
            ));
        } else {
            log_message('ERROR', json_encode($generateOpenAI));

            return JSONResponseDefault('FAILED', 'Gagal mengambil data');
        }
    }

    public function share()
    {
        $id = getPost('id');

        $get = $this->berita->get(array(
            'a.id' => $id,
            'a.isverified' => 1,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'URL' => base_url('berita/' . DateFormat($row->createddate, 'Y')) . '/' . $row->link_access . '?id=' . $row->id . '&embed=share'
        ));
    }
}
