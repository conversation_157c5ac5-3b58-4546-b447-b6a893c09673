<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Berita_Model $berita
 * @property Request_Domain $request_domain
 * @property Timeline_Task $timeline_task
 * @property Setting_Umum $setting_umum
 */
class Cronjobs extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Berita_Model', 'berita');
        $this->load->model('Request_Domain', 'request_domain');
        $this->load->model('Timeline_Task', 'timeline_task');
        $this->load->model('Setting_Umum', 'setting_umum');
    }

    public function shares()
    {
        $localhost = isLocalhost();
        $shares_urls = getSharesUrl();

        if (is_array($shares_urls) && count($shares_urls) > 0) {
            foreach ($shares_urls as $key => $value) {
                $shares = $value->shares;
                $url = $value->url;
                $exploding = explode('/', $url);

                if ($localhost) {
                    $link_access = $exploding[4];
                } else {
                    $link_access = $exploding[3];
                }

                $link_access = explode('?', $link_access)[0];

                $get = $this->berita->get(array(
                    'link_access' => $link_access
                ));

                if ($get->num_rows() > 0) {
                    $update = array();
                    $update['sharescount'] = $shares;

                    $this->berita->update(array(
                        'link_access' => $link_access
                    ), $update);
                }
            }
        }
    }

    public function cloudflare()
    {
        $processing = $this->request_domain->select('a.*, b.platformname')
            ->join('msusers b', 'b.id = a.userid')
            ->result(array(
                'status' => 'Processing'
            ));

        $cloudflare = new Cloudflare(String_Helper::CLOUDLFARE_ACCOUNT, String_Helper::CLOUDFLARE_TOKEN);

        foreach ($processing as $key => $value) {
            $domain = $value->domain;
            $usingsubdomain = (isDomain($domain) == false);

            $zonesdomain = $domain;
            $subdomain = null;
            if ($usingsubdomain) {
                $parts = explode('.', $domain);

                if (count($parts) > 0) {
                    $zonesdomain = $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];
                    $subdomain = $parts[0];
                } else {
                    continue;
                }
            }

            $zones = $cloudflare->zones(array(
                'name' => $zonesdomain
            ));

            if (isset($zones->result) && is_array($zones->result)) {
                if (count($zones->result) > 0) {
                    $zoneid = $zones->result[0]->id;
                    $dnsdetail = $cloudflare->list_dns_zone($zoneid);

                    $atype = false;
                    $cnametype = false;

                    if (isset($dnsdetail->success) && $dnsdetail->success) {
                        foreach ($dnsdetail->result as $k => $v) {
                            $content = $v->content;
                            $name = $v->name;
                            $type = $v->type;

                            if ($type == 'A' && $name == $value->domain) {
                                $atype = true;
                            }

                            if ($subdomain != null) {
                                if ($type == 'CNAME' && $name == 'www.' . $subdomain && $content == $value->domain) {
                                    $cnametype = true;
                                }
                            } else {
                                if ($type == 'CNAME' && $name == 'www' && $content == $value->domain) {
                                    $cnametype = true;
                                }
                            }
                        }
                    }

                    if (!$atype) {
                        if ($value->platformname == 'GidesManis') {
                            $cloudflare->create_dns_record($zoneid, "A", $value->domain, "34.120.3.135", 1, true);
                        } else {
                            $cloudflare->create_dns_record($zoneid, "A", $value->domain, "34.117.211.26", 1, true);
                        }
                    }

                    if (!$cnametype) {
                        if ($subdomain != null) {
                            $cloudflare->create_dns_record($zoneid, "CNAME", "www.$subdomain", $value->domain, 1, true);
                        } else {
                            $cloudflare->create_dns_record($zoneid, "CNAME", "www", $value->domain, 1, true);
                        }
                    }

                    $update = array();
                    $update['status'] = 'Wait for Pairing';
                    $update['updateddate'] = getCurrentDate();

                    $this->request_domain->update(array(
                        'id' => $value->id
                    ), $update);
                } else {
                    $create = $cloudflare->create_zone($zonesdomain);

                    if (isset($create->success) && $create->success) {
                        $zoneid = $create->result->id;

                        if ($value->platformname == 'GidesManis') {
                            $cloudflare->create_dns_record($zoneid, "A", $value->domain, "34.120.3.135", 1, true);
                        } else {
                            $cloudflare->create_dns_record($zoneid, "A", $value->domain, "34.117.211.26", 1, true);
                        }

                        if (!$usingsubdomain) {
                            $cloudflare->create_dns_record($zoneid, "CNAME", "www", $value->domain, 1, true);
                        } else {
                            if ($subdomain != null) {
                                $cloudflare->create_dns_record($zoneid, "CNAME", "www.$subdomain", $value->domain, 1, true);
                            } else {
                                $cloudflare->create_dns_record($zoneid, "CNAME", "www", $value->domain, 1, true);
                            }
                        }

                        $cloudflare->change_ssl_setting($zoneid, "strict");

                        $update = array();
                        $update['status'] = 'Wait for Pairing';
                        $update['updateddate'] = getCurrentDate();

                        $this->request_domain->update(array(
                            'id' => $value->id
                        ), $update);
                    } else {
                        $update = array();
                        $update['status'] = 'Failed';
                        $update['updateddate'] = getCurrentDate();

                        $this->request_domain->update(array(
                            'id' => $value->id
                        ), $update);
                    }
                }
            } else {
                $update = array();
                $update['status'] = 'Failed';
                $update['updateddate'] = getCurrentDate();

                $this->request_domain->update(array(
                    'id' => $value->id
                ), $update);
            }
        }
    }

    public function requestdomain()
    {
        $request_domain = $this->request_domain->result(array(
            'status' => 'Pending'
        ));

        foreach ($request_domain as $key => $value) {
            $update = array();
            $update['status'] = 'Processing';
            $update['updateddate'] = getCurrentDate();

            $this->request_domain->update(array(
                'id' => $value->id
            ), $update);
        }
    }

    public function cloudflare_pair()
    {
        $request_domain = $this->request_domain->result(array(
            'status' => 'Wait for Pairing'
        ));

        foreach ($request_domain as $key => $value) {
            $domain = $value->domain;
            $subdomain = null;

            if (isDomain($domain)) {
                $ns = dns_get_record($domain, DNS_NS);
            } else {
                $parts = explode('.', $domain);

                if (count($parts) > 2) {
                    $domain = $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];
                    $subdomain = $parts[0];

                    $ns = dns_get_record($domain, DNS_NS);
                } else {
                    $ns = array();
                }
            }

            foreach ($ns as $k => $v) {
                if (searchCloudflare($v['target'])) {
                    $found_cloudflare = true;
                }
            }

            $cloudflare = new Cloudflare(String_Helper::CLOUDLFARE_ACCOUNT, String_Helper::CLOUDFLARE_TOKEN);
            $zone = $cloudflare->zones(array('name' => $domain));

            if (count($zone->result) > 0) {
                $zoneid = $zone->result[0]->id;
                $dnsdetail = $cloudflare->list_dns_zone($zoneid);

                $atype = false;
                $cnametype = false;

                if (isset($dnsdetail->success) && $dnsdetail->success) {
                    foreach ($dnsdetail->result as $k => $v) {
                        $content = $v->content;
                        $name = $v->name;
                        $type = $v->type;

                        if ($type == 'A' && $name == $value->domain) {
                            $atype = true;
                        }

                        if ($type == 'CNAME' && $name == 'www.' . $value->domain && $content == $value->domain) {
                            $cnametype = true;
                        }
                    }
                }

                if ($atype && $cnametype) {
                    $valid_cloudflare = true;
                }

                if ($found_cloudflare && $valid_cloudflare) {
                    $update = array();
                    $update['status'] = 'Generating SSL';
                    $update['updateddate'] = getCurrentDate();

                    $this->request_domain->update(
                        array(
                            'id' => $value->id
                        ),
                        $update
                    );
                } else if ($found_cloudflare && !$valid_cloudflare) {
                    $update = array();
                    $update['status'] = 'Failed';
                    $update['updateddate'] = getCurrentDate();

                    $this->request_domain->update(
                        array(
                            'id' => $value->id
                        ),
                        $update
                    );

                    log_message('ERROR', 'Failed to Pairing domain: ' . $value->domain);
                }
            }
        }
    }

    public function auto_ssl()
    {
        $request_domain = $this->request_domain->result(array(
            'status' => 'Generating SSL'
        ));

        foreach ($request_domain as $key => $value) {
            $generate = generateSSL($value->domain);

            if (isset($generate->result) && $generate->result) {
                $update = array();
                $update['status'] = 'Success';
                $update['updateddate'] = getCurrentDate();

                $this->request_domain->update(array(
                    'id' => $value->id
                ), $update);
            }
        }
    }

    private function console_log($message)
    {
        $STDERR = fopen("php://stderr", "w");
        fwrite($STDERR, "\n" . $message . "\n\n");
        fclose($STDERR);
    }

    public function migrate_uploads()
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');

        // get all file in uploads folder
        $files = glob(FCPATH . 'uploads/*');

        foreach ($files as $key => $value) {
            // check if file is a directory
            if (!is_dir($value)) {
                // do upload to google storage
                try {
                    $upload = doUpload_CloudStorage($value, '*', 'file');

                    $this->console_log('File ' . $value . ' response: ' . json_encode($upload));
                } catch (Exception $ex) {
                    $this->console_log($ex->getMessage());
                }
            }
        }
    }

    public function reminder_timelinetask()
    {
        $timelinetask = $this->timeline_task->select('a.*, b.username')
            ->join('msusers b', 'b.id = a.createdby')
            ->result(array(
                'a.status' => 'Processing'
            ));

        foreach ($timelinetask as $key => $value) {
            $date = $value->deadline;
            $date = date('Y-m-d', strtotime($date . ' -7 day'));

            if (date('Y-m-d') >= $date) {
                $message = 'Halo, ' . $value->username . '! Deadline tugas ' . $value->task . ' akan segera tiba pada tanggal ' . tgl_indo($value->deadline) . '. Segera selesaikan tugas tersebut ya!';

                $setting_umum = $this->setting_umum->get(array(
                    'id_user' => $value->createdby
                ));

                if ($setting_umum->num_rows() > 0) {
                    $setting_umum = $setting_umum->row();
                    $kontak = $setting_umum->kontak;

                    sendMessageWhatsapp($kontak, $message);
                }
            }
        }
    }
}
