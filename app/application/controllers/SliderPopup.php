<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Class SliderPopup
 * 
 * @property Slider_Popup $slider_popup
 */
class SliderPopup extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Slider_Popup', 'slider_popup');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = "Slider Popup";
        $data['content'] = 'slider_popup/index';
        $data['slider_popup'] = $this->slider_popup->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isSuperAdmin()) {
            return redirect('dashboard');
        }

        $data = array();
        $data['title'] = 'Setting Popup - Add';
        $data['content'] = 'slider_popup/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        try {
            $upload = doUpload_CloudStorage('slider', 'jpg|jpeg|png');

            $insert = array();
            $insert['slider'] = $upload['name'];

            $insert = $this->slider_popup->insert($insert);

            if ($insert) {
                return JSONResponseDefault('OK', 'Slider berhasil ditambahkan');
            } else {
                return JSONResponseDefault('FAILED', 'Gagal menambahkan slider');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->slider_popup->getDefaultData(array(
            'a.id' => $id,
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data not found');
        }

        $delete = $this->slider_popup->delete(array(
            'id' => $id
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }
}
