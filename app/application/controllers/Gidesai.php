<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Gidesai extends MY_Controller
{
    public function start()
    {
        $message = getPost('message');

        if ($message == null) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Pesan tidak boleh kosong'
            ));
        }

        $generateOpenAI = generateOpenAI($message);

        if (isset($generateOpenAI->result) && $generateOpenAI->result) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'DESCRIPTION' => $generateOpenAI->message,
                'NOT_ANSWER' => is_openai_not_answer($generateOpenAI->message),
            ));
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengambil data');
        }
    }
}
