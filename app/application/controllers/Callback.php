<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Incoming_WhatsApp $incomingwhatsapp
 * @property MsAutoreplywa $msautoreplywa
 * @property MsLetterAccess $msletteraccess
 * @property LogConversation $logconversation
 * @property Kategori_Surat $kategorisurat
 * @property DetailKategori_Surat $detailkategorisurat
 * @property Model_Surat $model_surat
 * @property Field_Surat $fieldsurat
 * @property Surat_Model $surat
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 */
class Callback extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Incoming_WhatsApp', 'incomingwhatsapp');
        $this->load->model('MsAutoreplywa', 'msautoreplywa');
        $this->load->model('MsLetterAccess', 'msletteraccess');
        $this->load->model('LogConversation', 'logconversation');
        $this->load->model('Kategori_Surat', 'kategorisurat');
        $this->load->model('DetailKategori_Surat', 'detailkategorisurat');
        $this->load->model('Model_Surat', 'model_surat');
        $this->load->model('Field_Surat', 'fieldsurat');
        $this->load->model('Surat_Model', 'surat');
    }

    public function wablas()
    {
        $content = json_decode(file_get_contents('php://input'), true);

        $id = $content['id'];
        $pushName = $content['pushName'];
        $isGroup = $content['isGroup'];

        if ($isGroup == true) {
            $subjectGroup = $content['group']['subject'];
            $ownerGroup = $content['group']['owner'];
            $decriptionGroup = $content['group']['desc'];
            $partisipanGroup = $content['group']['participants'];
        }

        $message = $content['message'];
        $phone = $content['phone'];
        $messageType = $content['messageType'];
        $file = $content['file'];
        $mimeType = $content['mimeType'];
        $deviceId = $content['deviceId'];
        $sender = $content['sender'];
        $timestamp = $content['timestamp'];

        $insert = array();
        $insert['message_id'] = $id;
        $insert['push_name'] = $pushName;
        $insert['message'] = $message;
        $insert['phone'] = $phone;
        $insert['createddate'] = getCurrentDate();

        $this->incomingwhatsapp->insert($insert);

        $get = $this->msautoreplywa->get(array(
            'LOWER(keyword) =' => strtolower($message),
            'id_user' => $this->subdomain_account->id
        ));

        if ($get->num_rows() > 0) {
            $row = $get->row();

            sendMessageWhatsapp($phone, $row->response);
        }
    }

    public function whatsapp()
    {
        $conversationphone = getPost('conversationphone');
        $conversation = getPost('conversation');

        if ($conversationphone == null || $conversation == null) {
            $_POST = json_decode(file_get_contents('php://input'), true);
            $conversationphone = $_POST['conversationphone'];
            $conversation = $_POST['conversation'];
        }

        log_message('error', "Phone: $conversationphone, Conversation: $conversation");

        $get = $this->logconversation->get(array(
            'phonenumber' => $conversationphone,
            'is_processed' => null
        ));

        $exploding_conversation = explode(' ', $conversation);

        if ($get->num_rows() == 0) {
            if (strtolower($exploding_conversation[0]) == '.surat') {
                $nik = $exploding_conversation[1] ?? null;

                if ($nik == null) {
                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'RESPONSE' => 'Mohon masukkan NIK Anda'
                    ));
                } else if (!is_numeric($nik)) {
                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'RESPONSE' => 'NIK harus berupa angka'
                    ));
                } else if (!strlen($nik) == 16) {
                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'RESPONSE' => 'NIK harus berjumlah 16 digit'
                    ));
                } else {
                    $surat = $this->msletteraccess->select('b.nama, a.categoryid')
                        ->join('kategori_surat b', 'b.id = a.categoryid')
                        ->result(array(
                            'a.userid' => $this->subdomain_account->id
                        ));

                    $command = "";
                    foreach ($surat as $key => $value) {
                        $lettercategoryid = $value->categoryid;
                        $command .= $key + 1 . ". " . $value->nama . " (Kode: $lettercategoryid)\n";
                    }

                    $insert = array();
                    $insert['user_id'] = $this->subdomain_account->id;
                    $insert['phonenumber'] = $conversationphone;
                    $insert['conversation'] = $conversation;
                    $insert['action'] = 'SELECT_SURAT';
                    $insert['createddate'] = getCurrentDate();

                    $this->logconversation->insert($insert);

                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'RESPONSE' => "Berikut adalah daftar surat yang tersedia:\n$command\nSilahkan pilih surat yang ingin Anda akses dengan mengetikkan kode surat yang diinginkan"
                    ));
                }
            } else {
                return JSONResponseDefault('FAILED', 'No response found');
            }
        } else {
            $zero_conversation = $exploding_conversation[0] ?? null;

            if (strtolower($zero_conversation) == '.reset') {
                $update = array();
                $update['is_processed'] = 1;

                $this->logconversation->update(array(
                    'phonenumber' => $conversationphone,
                    'is_processed' => null
                ), $update);

                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'RESPONSE' => 'Obrolan Anda telah direset'
                ));
            } else {
                $row = $get->row();

                if ($row->action == 'SELECT_SURAT') {
                    $code = $exploding_conversation[0] ?? null;

                    if (!is_numeric($code)) {
                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'RESPONSE' => 'Masukkan kode surat yang benar'
                        ));
                    } else {
                        $get = $this->kategorisurat->get(array(
                            'id' => $code,
                        ));

                        if ($get->num_rows() == 0) {
                            return JSONResponse(array(
                                'RESULT' => 'OK',
                                'RESPONSE' => 'Kode surat tidak ditemukan'
                            ));
                        } else {
                            $update = array();
                            $update['is_processed'] = 1;

                            $this->logconversation->update(array(
                                'id' => $row->id
                            ), $update);

                            $detailkategori_surat = $this->detailkategorisurat->select('b.id, b.name, b.position AS jabatan')
                                ->join('mspenandatangan b', 'b.id = a.penandatanganid')
                                ->group_by('b.id, b.name, b.position')
                                ->result(array(
                                    'a.catsuratid' => $code,
                                    'a.id_user' => $this->subdomain_account->id
                                ));

                            $insert = array();
                            $insert['user_id'] = $this->subdomain_account->id;
                            $insert['phonenumber'] = $conversationphone;
                            $insert['conversation'] = $conversation;
                            $insert['action'] = 'SELECT_PENANDATANGAN';
                            $insert['createddate'] = getCurrentDate();
                            $insert['reference_id'] = $row->id;

                            $this->logconversation->insert($insert);

                            $command = "";
                            foreach ($detailkategori_surat as $key => $value) {
                                $command .= $key + 1 . ". " . $value->name . " (Jabatan: " . $value->jabatan . ") (Kode: $value->id)\n";
                            }

                            return JSONResponse(array(
                                'RESULT' => 'OK',
                                'RESPONSE' => "Berikut adalah daftar penandatangan surat yang tersedia:\n$command\nSilahkan pilih penandatangan surat dengan mengetikkan kode penandatangan yang diinginkan"
                            ));
                        }
                    }
                } else if ($row->action == 'SELECT_PENANDATANGAN') {
                    $code = $exploding_conversation[0] ?? null;

                    if (!is_numeric($code)) {
                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'RESPONSE' => 'Masukkan kode penandatangan yang benar'
                        ));
                    } else {
                        $update = array();
                        $update['is_processed'] = 1;

                        $this->logconversation->update(array(
                            'id' => $row->id
                        ), $update);

                        $modelsurat = $this->model_surat->result(array(
                            'categorylettersid' => $row->conversation,
                        ));

                        $insert = array();
                        $insert['user_id'] = $this->subdomain_account->id;
                        $insert['phonenumber'] = $conversationphone;
                        $insert['conversation'] = $conversation;
                        $insert['action'] = 'SELECT_MODEL_SURAT';
                        $insert['createddate'] = getCurrentDate();
                        $insert['reference_id'] = $row->id;

                        $this->logconversation->insert($insert);

                        $command = "";
                        foreach ($modelsurat as $key => $value) {
                            $command .= $key + 1 . ". " . $value->modelname . " (Kode: $value->id)\n";
                        }

                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'RESPONSE' => "Berikut adalah daftar model surat yang tersedia:\n$command\nSilahkan pilih model surat dengan mengetikkan kode model surat yang diinginkan"
                        ));
                    }
                } else if ($row->action == 'SELECT_MODEL_SURAT') {
                    $code = $exploding_conversation[0] ?? null;

                    if (!is_numeric($code)) {
                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'RESPONSE' => 'Masukkan kode model surat yang benar'
                        ));
                    } else {
                        $update = array();
                        $update['is_processed'] = 1;

                        $this->logconversation->update(array(
                            'id' => $row->id
                        ), $update);

                        $field_surat = $this->fieldsurat->order_by('a.sequence', 'ASC')
                            ->result(array(
                                'modelid' => $code,
                            ));

                        $insert = array();
                        $insert['user_id'] = $this->subdomain_account->id;
                        $insert['phonenumber'] = $conversationphone;
                        $insert['conversation'] = $conversation;
                        $insert['action'] = 'SELECT_FIELD_SURAT';
                        $insert['createddate'] = getCurrentDate();
                        $insert['reference_id'] = $row->id;

                        $this->logconversation->insert($insert);

                        $command = "";
                        foreach ($field_surat as $key => $value) {
                            if ($value->is_required == 1) {
                                $command .= "*$value->input_label ($value->variablename)*: \n";
                            } else {
                                $command .= "$value->input_label ($value->variablename): \n";
                            }
                        }

                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'RESPONSE' => "Silahkan masukkan data-data yang diperlukan untuk surat ini dengan cara *salin seluruh pesan ini*:\n\n$command\n*Data yang bercetak tebal wajib diisi"
                        ));
                    }
                } else if ($row->action == 'SELECT_FIELD_SURAT') {
                    $format = $conversation;
                    $explode_format = explode("\n", $format);

                    $field_surat = $this->fieldsurat->order_by('a.sequence', 'ASC')
                        ->result(array(
                            'modelid' => $row->conversation,
                        ));

                    $formats = array();
                    foreach ($field_surat as $key => $value) {
                        $formula = "*$value->input_label ($value->variablename)*:";

                        // get the value of the formula from explode_format
                        $val = null;
                        foreach ($explode_format as $explode) {
                            if (strpos($explode, $formula) !== false) {
                                $val = trim(str_replace($formula, '', $explode) ?? '');
                                break;
                            }
                        }

                        $formats[] = array(
                            'variablename' => $value->variablename,
                            'value' => $val
                        );
                    }

                    $modelsurat_id = $row->conversation;
                    $penandatangan = $this->logconversation->get(array(
                        'id' => $row->reference_id
                    ))->row();
                    $penandatangan_id = $penandatangan->conversation;
                    $surat = $this->logconversation->get(array(
                        'id' => $penandatangan->reference_id
                    ))->row();
                    $surat_id = $surat->conversation;
                    $nik = $this->logconversation->get(array(
                        'id' => $surat->reference_id
                    ))->row()->conversation;
                    $explode_nik = explode(' ', $nik)[1];

                    $insert = array();
                    $insert['jenis_surat'] = $surat_id;
                    $insert['nik'] = $explode_nik;
                    $insert['nomor_hp'] = $conversationphone;
                    $insert['tanggal_permohonan'] = getCurrentDate();
                    $insert['penandatanganid'] = $penandatangan_id;
                    $insert['status'] = 'Menunggu';
                    $insert['tanggal_status'] = getCurrentDate();
                    $insert['tipe_surat'] = 'otomatis';
                    $insert['nomor_surat'] = replaceFormatSurat($this->subdomain_account->id, getCurrentSettingUmum($this->subdomain_account->id)->format_surat, $modelsurat_id);
                    $insert['modelid'] = $modelsurat_id;
                    $insert['field_json'] = json_encode($formats);

                    $this->surat->insert($insert);
                    $id_surat = $this->db->insert_id();

                    $link = base_url('surat/permohonan/baru/id/' . $id_surat);

                    $contact = $this->detailkategorisurat->result(array(
                        'a.catsuratid' => $surat_id,
                        'a.id_user' => $this->subdomain_account->id,
                        'a.id' => $penandatangan_id
                    ));

                    $message = "Ada permohonan surat baru dari warga anda. Silahkan cek dan lakukan approve dengan cara KLIK link berikut ini : $link";

                    foreach ($contact as $key => $value) {
                        $telepon = $value->nomor_handphone;

                        sendMessageWhatsapp($telepon, $message);
                    }

                    $update = array();
                    $update['is_processed'] = 1;

                    $this->logconversation->update(array(
                        'id' => $row->id
                    ), $update);

                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'RESPONSE' => 'Permohonan surat berhasil dibuat. Silahkan tunggu konfirmasi dari pihak penandatangan'
                    ));
                }
            }
        }
    }
}
