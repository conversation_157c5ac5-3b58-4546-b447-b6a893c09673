<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Timeline_Task $timeline_task
 * @property Master_Users $msusers
 * @property Operator_Desa $operatordesa
 */
class TimelineTask extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Timeline_Task', 'timeline_task');
        $this->load->model('Master_Users', 'msusers');
        $this->load->model('Operator_Desa', 'operatordesa');
    }

    public function index()
    {
        $data = array();
        $data['title'] = 'Timeline Tugas';
        $data['content'] = 'timeline_task/index';
        if (!isOperatorDesa()) {
            $data['timelinetask'] = $this->timeline_task->select('a.*, b.role')
                ->join('msusers b', 'b.id = a.approved_by', 'LEFT')
                ->result(array(
                    'a.createdby' => getCurrentIdUser(true)
                ));
        } else {
            $data['timelinetask'] = $this->timeline_task->select('a.*, b.role')
                ->join('msusers b', 'b.id = a.approved_by', 'LEFT')
                ->where(array(
                    'a.operatordesaid' => getCurrentIdUser(true)
                ))
                ->result();
        }

        return $this->load->view('master', $data);
    }

    public function request()
    {
        $where = array();
        $where_in = array();

        $currentuser = $this->msusers->get(array('id' => getCurrentIdUser(true)))->row();

        if (isKecamatan()) {
            $where['c.id_kecamatan'] = $currentuser->kecamatanid;
        } elseif (isPMD()) {
            $kecamatanid = explode(',', $currentuser->kecamatanid);
            $where_in = $kecamatanid;
        }

        $data = array();
        $data['title'] = 'Permohonan Timeline Tugas';
        $data['content'] = 'timeline_task/index';
        $data['timelinetask'] = $this->timeline_task->select('a.*')
            ->join('msusers b', 'b.id = a.createdby')
            ->join('kelurahan c', 'c.id_kelurahan = b.kelurahanid')
            ->where($where)
            ->where_in('c.id_kecamatan', $where_in)
            ->result(array(
                'a.status' => 'Waiting Verification'
            ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        $data = array();
        $data['title'] = 'Tambah Timeline Tugas';
        $data['content'] = 'timeline_task/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        $deadline = getPost('deadline');
        $task = getPost('task');
        $description = getPost('description');

        $insert = array();
        $insert['deadline'] = $deadline;
        $insert['task'] = $task;
        $insert['description'] = $description;
        $insert['status'] = 'Pending';

        if (!isOperatorDesa()) {
            $insert['createdby'] = getCurrentIdUser(true);
        } else {
            $currentuser = $this->operatordesa->get(array('id' => getCurrentIdUser(true)))->row();

            $insert['operatordesaid'] = getCurrentIdUser(true);
            $insert['createdby'] = $currentuser->createdby;
        }

        $this->timeline_task->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function edit($id)
    {
        if (!isOperatorDesa()) {
            $get = $this->timeline_task->get(array('id' => $id, 'createdby' => getCurrentIdUser(true)));
        } else {
            $get = $this->timeline_task->get(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)));
        }

        if ($get->num_rows() == 0) {
            return redirect('timelinetask');
        }

        $data = array();
        $data['title'] = 'Edit Timeline Tugas';
        $data['content'] = 'timeline_task/edit';
        $data['timelinetask'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        $deadline = getPost('deadline');
        $task = getPost('task');
        $description = getPost('description');

        $update = array();
        $update['deadline'] = $deadline;
        $update['task'] = $task;
        $update['description'] = $description;

        if (!isOperatorDesa()) {
            $this->timeline_task->update(array('id' => $id, 'createdby' => getCurrentIdUser(true)), $update);
        } else {
            $this->timeline_task->update(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)), $update);
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }

    public function process_delete()
    {
        $id = getPost('id');

        if (!isOperatorDesa()) {
            $this->timeline_task->delete(array('id' => $id, 'createdby' => getCurrentIdUser(true)));
        } else {
            $this->timeline_task->delete(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)));
        }

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }

    public function release()
    {
        $id = getPost('id');

        $update = array();
        $update['isrelease'] = 1;
        $update['status'] = 'Processing';

        if (!isOperatorDesa()) {
            $this->timeline_task->update(array('id' => $id, 'createdby' => getCurrentIdUser(true)), $update);
        } else {
            $this->timeline_task->update(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)), $update);
        }

        return JSONResponseDefault('OK', 'Data berhasil direlease');
    }

    public function send_document()
    {
        $id = getPost('id');

        if (!isOperatorDesa()) {
            $get = $this->timeline_task->get(array('id' => $id, 'createdby' => getCurrentIdUser(true)));
        } else {
            $get = $this->timeline_task->get(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)));
        }

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('timeline_task/send_document', array(
                'timelinetask' => $get->row()
            ), true)
        ));
    }

    public function process_send_document()
    {
        $id = getPost('id');

        if (!isOperatorDesa()) {
            $get = $this->timeline_task->get(array('id' => $id, 'createdby' => getCurrentIdUser(true)));
        } else {
            $get = $this->timeline_task->get(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)));
        }

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        try {
            $document = doUpload_CloudStorage('document', 'pdf');
            $document = $document['name'];
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }

        $update = array();
        $update['document'] = $document;
        $update['status'] = 'Waiting Verification';

        if (!isOperatorDesa()) {
            $this->timeline_task->update(array('id' => $id, 'createdby' => getCurrentIdUser(true)), $update);
        } else {
            $this->timeline_task->update(array('id' => $id, 'operatordesaid' => getCurrentIdUser(true)), $update);
        }

        return JSONResponseDefault('OK', 'Dokumen berhasil dikirim');
    }

    public function verification()
    {
        $id = getPost('id');

        $get = $this->timeline_task->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $update = array();
        $update['status'] = 'Approved';
        $update['approved_by'] = getCurrentIdUser(true);

        $this->timeline_task->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diverifikasi');
    }
}
