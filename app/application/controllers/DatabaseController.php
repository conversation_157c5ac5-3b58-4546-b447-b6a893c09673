<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DatabaseController extends MY_Controller
{
    public function backups()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (isAdmin() || isPMD()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Database Backups';
        $data['content'] = 'database/backups';

        $dir = scandir('./backups/');

        $files = array();
        foreach ($dir as $key => $value) {
            if ($value != '.' && $value != '..') {
                $explodes = explode('-', $value);
                $datetime = $explodes[1];
                $date = explode('_', $datetime)[0];

                if (date('Y-m') == date('Y-m', strtotime($date))) {
                    $modifieddate = filemtime('./backups/' . $value);
                    $modifieddate = date('Y-m-d H:i:s', $modifieddate);
                    $files[$modifieddate] = $value;
                }
            }
        }

        asort($files);
        $data['file'] = $files;

        $this->load->view('master', $data);
    }
}
