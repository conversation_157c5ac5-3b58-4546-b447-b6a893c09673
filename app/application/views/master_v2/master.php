<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Gides - Dashboard</title>

    <link rel="stylesheet" href="<?= asset_url() ?>assets/dashboard/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/dashboard/css/style.css">
    <link href="<?= asset_url() ?>assets/extra-libs/datatables.net-bs4/css/dataTables.bootstrap4.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css" integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <style>
        .ai-popup .card {
            max-width: 400px;
            min-width: 400px;
            box-shadow: 1px 1px 3px #888888;
        }

        .ai-popup .card .card-body {
            max-height: 400px;
            overflow: auto;
        }

        .ai-popup .card .card-header .card-title {
            margin-bottom: 0;
        }

        .chat-popup {
            position: fixed;
            bottom: -100%;
            transition: all .25s ease-in-out;
            right: 2rem;
        }

        .chat-popup.show {
            bottom: 5rem;
        }

        .chat-logo {
            position: relative;
        }

        .chat-logo.hidden {
            left: -10rem;
        }

        .dot {
            display: inline-block;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: #4b9cdb;
        }

        @keyframes jumpingAnimation {
            0% {
                transform: translate3d(0, 0, 0);
            }

            50% {
                transform: translate3d(0, 3px, 0);
            }

            100% {
                transform: translate3d(0, 0, 0);
            }
        }

        .dot-container {
            display: flex;
            padding-top: 3px;
            padding-bottom: 7px;
        }

        .dot-container .dot:nth-last-child(1) {
            animation: jumpingAnimation 0.6s 0.1s ease-in infinite;
            margin-right: 5px;
        }

        .dot-container .dot:nth-last-child(2) {
            animation: jumpingAnimation 0.6s 0.2s ease-in infinite;
            margin-right: 5px;
        }

        .dot-container .dot:nth-last-child(3) {
            animation: jumpingAnimation 0.6s 0.3s ease-in infinite;
            margin-right: 5px;
        }
    </style>
</head>

<body>
    <div class="loading-ajax" style="position: fixed;top: 0;left: 0;right: 0;bottom: 0;background: rgb(255 255 255 / 75%);z-index: 99999;display: flex;justify-content: center;align-items: center;flex-direction: column;opacity: 0;transition-duration: .25s;transition-timing-function: ease-in-out;visibility: hidden;">
        <div class="text-center d-flex flex-column justify-content-center">
            <?php if (getPlatformName() == 'Gides') : ?>
                <img src="<?= asset_url() ?>assets/images/gides-logo.png" alt="" width="25%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'GidesManis') : ?>
                <img src="<?= asset_url() ?>assets/images/gidesmanis-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'SmartDes') : ?>
                <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'ProGides') : ?>
                <img src="<?= asset_url() ?>assets/images/progides-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php endif; ?>

            <div class="spinner-border text-primary m-auto" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    </div>

    <div class="main-wrapper">
        <div class="sidebar">
            <div class="sidebar-header">
                <?php if (getPlatformName() == 'Gides') : ?>
                    <img src="<?= asset_url() ?>assets/images/gides-logo.png" alt="" width="50">
                <?php elseif (getPlatformName() == 'GidesManis') : ?>
                    <img src="<?= asset_url() ?>assets/images/gidesmanis-logo.png" alt="" width="50">
                <?php elseif (getPlatformName() == 'SmartDes') : ?>
                    <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="" width="50">
                <?php elseif (getPlatformName() == 'ProGides') : ?>
                    <img src="<?= asset_url() ?>assets/images/progides-logo.png" alt="" width="50">
                <?php endif; ?>

                <button type="button" class="custom-navbar-toggler">
                    <i class="fa fa-arrow-left"></i>
                </button>
            </div>

            <div class="sidebar-content">
                <ul>
                    <?php if (!isOperatorDesa()): ?>
                        <li class="active">
                            <a href="<?= base_url('admin') ?>">
                                <i class="fa fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <!-- Master -->
                        <?php if (isSuperAdmin() || isAdmin() || isDiskominfo()) : ?>
                            <li>
                                <a href="#master" class="has-arrow" data-toggle="custom-toggle">
                                    <i class="fa fa-cogs"></i>
                                    <span>Master</span>
                                </a>


                                <ul class="sub-menu" id="master">
                                    <?php if (isSuperAdmin() || isDiskominfo()) : ?>
                                        <?php if (isSuperAdmin() && isAllPlatform()): ?>
                                            <li>
                                                <a href="<?= base_url('manage/superadmin') ?>">Super Admin</a>
                                            </li>
                                        <?php endif; ?>

                                        <li>
                                            <a href="<?= base_url('manage/admin') ?>">Admin</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php if (isSuperAdmin() && isAllPlatform()) : ?>
                                        <li>
                                            <a href="<?= base_url('manage/diskominfo') ?>">Diskominfo</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('manage/pmd') ?>">PMD</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('manage/regency') ?>">Kabupaten</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('manage/subdistrict') ?>">Kecamatan</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php if (isSuperAdmin()): ?>
                                        <li>
                                            <a href="<?= base_url('manage/category/letters') ?>">Kategori Surat</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php if (isSuperAdmin()): ?>
                                        <?php if (isAllPlatform()): ?>
                                            <li>
                                                <a href="<?= base_url('manage/category/budgets') ?>">Kategori Anggaran</a>
                                            </li>

                                            <li>
                                                <a href="<?= base_url('manage/category/news') ?>">Kategori Berita</a>
                                            </li>

                                            <li>
                                                <a href="<?= base_url('manage/news/notification') ?>">Notifikasi Berita</a>
                                            </li>
                                        <?php endif; ?>

                                        <li>
                                            <a href="<?= base_url('manage/news') ?>">Berita</a>
                                        </li>

                                        <?php if (isAllPlatform()): ?>
                                            <li>
                                                <a href="<?= base_url('manage/slider_popup') ?>">Slider Popup</a>
                                            </li>
                                        <?php endif; ?>
                                    <?php elseif (isAdmin()) : ?>
                                        <li>
                                            <a href="<?= base_url('master/bidangpengaduan') ?>">
                                                Bidang Pengaduan
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('master/bidangperangkat') ?>">
                                                Bidang Perangkat Desa
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('master/kategoridokumen') ?>">
                                                Kategori Dokumen
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('warga') ?>">Data Warga</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('penandatangan') ?>">Penandatangan</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('asset') ?>">Aset</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('operator_desa') ?>">Operator Desa</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('autoreplywa') ?>">Auto Reply WA</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                        <!-- end master -->

                        <!-- SUPER ADMIN -->
                        <?php if (isSuperAdmin()) : ?>
                            <li>
                                <a href="<?= base_url('master/user_guide') ?>">
                                    <i class="fa fa-book"></i>
                                    <span>Buku Panduan</span>
                                </a>
                            </li>

                            <!-- <li>
                            <a href="<?= base_url('database/siskeudes/tapin') ?>">
                                <i class="fa fa-database"></i>
                                <span>Database Siskeudes [Tapin]</span>
                            </a>
                        </li> -->

                            <?php if (isAllPlatform()): ?>
                                <li>
                                    <a href="<?= base_url('adsense/reports') ?>">
                                        <i class="fa fa-dollar-sign"></i>
                                        <span class="hide-menu">Laporan Adsense</span>
                                    </a>
                                </li>

                                <li>
                                    <a href="<?= base_url('manage/whatsapp') ?>">
                                        <i class="fab fa-whatsapp"></i>
                                        <span>Setting WhatsApp</span>
                                    </a>
                                </li>

                                <li>
                                    <a href="<?= base_url('manage/prompt') ?>">
                                        <i class="fa fa-bell"></i>
                                        <span>Setting Prompt</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>
                        <!-- END SUPER ADMIN -->

                        <!-- DESA -->
                        <?php if (isAdmin() || isKecamatan_withWeb()) : ?>
                            <li>
                                <a href="#konten" class="has-arrow" data-toggle="custom-toggle">
                                    <i class="fa fa-file"></i>
                                    <span>Konten</span>
                                </a>

                                <ul class="sub-menu" id="konten">
                                    <li>
                                        <a href="<?= base_url('berita') ?>">Berita</a>
                                    </li>

                                    <li>
                                        <a href="<?= base_url('produk') ?>">Produk</a>
                                    </li>

                                    <?php if (isAdmin()) : ?>
                                        <li>
                                            <a href="<?= base_url('produkhukum') ?>">Produk Hukum</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('wisata') ?>">Wisata</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('slider') ?>">Slider</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('floworganizationchart') ?>">Alur Jabatan Struktur Organisasi</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('strukturorganisasi') ?>">Struktur Organisasi</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </li>

                            <?php if (isAdmin() || isKecamatan_withWeb()) : ?>
                                <?php if (isAdmin()) : ?>
                                    <li>
                                        <a href="#desa" class="has-arrow" data-toggle="custom-toggle">
                                            <i class="fa fa-home"></i>
                                            <span>Program</span>
                                        </a>

                                        <ul class="sub-menu" id="desa">
                                            <li>
                                                <a href="<?= base_url(isKelurahan() ? 'anggarankelurahan' : 'apbdesa') ?>"><?= isKelurahan() ? 'Anggaran Kelurahan' : 'APB Desa' ?></a>
                                            </li>

                                            <li>
                                                <a href="<?= base_url('timelinetask') ?>">Timeline Tugas</a>
                                            </li>

                                            <li>
                                                <a href="<?= base_url('penerimaan_blt') ?>">Penerimaan BLT</a>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                <li>
                                    <a href="#infografis" class="has-arrow" data-toggle="custom-toggle">
                                        <i class="fa fa-chart-bar"></i>
                                        <span>Infografis</span>
                                    </a>

                                    <ul class="sub-menu" id="infografis">
                                        <li>
                                            <a href="<?= base_url('kategori_infografis') ?>">Kategori</a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('infografis') ?>">Data Penduduk</a>
                                        </li>

                                        <?php if (isAdmin()) : ?>
                                            <li>
                                                <a href="<?= base_url('migrasi') ?>">Migrasi Antar-Desa</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </li>

                                <?php if (isAdmin() || isKecamatan_withWeb()) : ?>
                                    <?php if (isAdmin()) : ?>
                                        <li>
                                            <a href="#setting" class="has-arrow" data-toggle="custom-toggle">
                                                <i class="fa fa-cogs"></i>
                                                <span>Setting</span>
                                            </a>

                                            <ul class="sub-menu" id="setting">
                                                <li>
                                                    <a href="<?= base_url('settingumum') ?>">Setting Umum</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('settingmap') ?>">Batas Wilayah</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('kontakpenting') ?>">Kontak Penting</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('kategori_surat') ?>">Kategori Surat</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('whatsappgateway') ?>">WhatsApp Gateway</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('theme') ?>">Tema</a>
                                                </li>

                                                <li>
                                                    <a href="<?= base_url('siades') ?>">Sistem Absensi Desa</a>
                                                </li>
                                            </ul>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('surat') ?>">
                                                <i class="fa fa-envelope"></i>
                                                <span>Surat</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <li>
                                        <a href="<?= base_url('arsip/guestbook') ?>">
                                            <i class="fa fa-archive"></i>
                                            <span>Arsip Buku Tamu</span>
                                        </a>
                                    </li>

                                    <?php if (isAdmin()) : ?>
                                        <li>
                                            <a href="<?= base_url('master/user_guide') ?>">
                                                <i class="fa fa-book"></i>
                                                <span>Buku Manual</span>
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('arsip/pengaduan') ?>">
                                                <i class="fa fa-archive"></i>
                                                <span>Arsip Pengaduan</span>
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('surat/arsip/masuk') ?>">
                                                <i class="fa fa-archive"></i>
                                                <span>Arsip Surat Masuk</span>
                                            </a>
                                        </li>

                                        <li>
                                            <a href="<?= base_url('surat/arsip/keluar') ?>">
                                                <i class="fa fa-archive"></i>
                                                <span>Arsip Surat Keluar</span>
                                            </a>
                                        </li>

                                        <li>
                                            <a href="https://asset.gides.id/desktop/download/gides_installer.msi">
                                                <i class="fa fa-download"></i>
                                                <?php if (getPlatformName() != 'SmartDes' && getPlatformName() != 'ProGides'): ?>
                                                    <span>Gides Installer</span>
                                                <?php else: ?>
                                                    <?php if (getPlatformName() == 'SmartDes'): ?>
                                                        <span>SmartDes Installer</span>
                                                    <?php else: ?>
                                                        <span>ProGides Installer</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                        <!-- END DESA -->

                        <?php if (isKecamatan() || isPMD()) : ?>
                            <?php if (isKecamatan()) : ?>
                                <li>
                                    <a href="<?= base_url('strukturorganisasi/permohonan') ?>">
                                        <i class="fa fa-users"></i>
                                        <span>Permohonan Struktur Organisasi</span>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <li>
                                <a href="<?= base_url('timelinetask/request') ?>">
                                    <i class="fa fa-tasks"></i>
                                    <span>Permohonan Timeline Tugas</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- PMD -->
                        <?php if (isPMD()) : ?>
                            <li>
                                <a href="<?= base_url('pmd/leader') ?>">
                                    <i class="fa fa-users"></i>
                                    <span>Pimpinan PMD</span>
                                </a>
                            </li>

                            <li>
                                <a href="<?= base_url('pmd/member') ?>">
                                    <i class="fa fa-users"></i>
                                    <span>Anggota PMD</span>
                                </a>
                            </li>

                            <li>
                                <a href="<?= base_url('pmd/disposisi') ?>">
                                    <i class="fa fa-recycle"></i>
                                    <span>Disposisi</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <!-- END PMD -->
                    <?php else: ?>
                        <li>
                            <a href="<?= base_url('timelinetask') ?>">
                                <i class="fa fa-list"></i>
                                <span>Timeline Tugas</span>
                            </a>
                        </li>

                        <?php foreach (getMenu() as $key => $value): ?>
                            <?php if ($value->has_submenu != 1): ?>
                                <li class="<?= $value->url == 'admin' && (uri_string() == 'dashboard' || uri_string() == 'admin') ? 'active' : null ?>">
                                    <a href="<?= base_url($value->url) ?>">
                                        <i class="<?= $value->icon ?>"></i>
                                        <span><?= $value->name ?></span>
                                    </a>
                                </li>
                            <?php else: ?>
                                <li>
                                    <a href="#menu-<?= $value->id ?>" class="has-arrow" data-toggle="custom-toggle">
                                        <i class="<?= $value->icon ?>"></i>
                                        <span><?= $value->name ?></span>
                                    </a>

                                    <ul class="sub-menu" id="menu-<?= $value->id ?>">
                                        <?php foreach (getSubMenu($value->id) as $k => $v): ?>
                                            <li>
                                                <a href="<?= base_url($v->url) ?>">
                                                    <span><?= $v->name ?></span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <div class="content">
            <?php $this->load->view('master_v2/navbar') ?>

            <?php if (isset($content)) : ?>
                <?php $this->load->view($content); ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="modal fade" id="ModalGlobal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    </div>

    <div class="modal fade" id="ModalSlider" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content bg-transparent border-0">
                <div class="modal-body p-0">
                    <div class="slick-slider">
                        <?php foreach ($sliderpopup as $key => $value): ?>
                            <div>
                                <img src="<?= asset_url($value->slider) ?>" alt="" class="w-100">
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-2">
                        <a href="<?= base_url(uri_string() . '?mark=1') ?>" class="text-decoration-none text-white small">Jangan tampilkan untuk sesi kali ini</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="position: fixed; z-index: 1; right: 1.5rem; bottom: 1.5rem; cursor: pointer;" class="ai-popup">
        <div class="chat-popup">
            <div class="card">
                <div class="card-header">
                    <div>
                        <?php if (getPlatformName() != 'SmartDes' && getPlatformName() != 'ProGides'): ?>
                            <h4 class="card-title">Gides.ai - Web</h4>
                        <?php else: ?>
                            <?php if (getPlatformName() == 'SmartDes'): ?>
                                <h4 class="card-title">SmartDes.ai - Web</h4>
                            <?php else: ?>
                                <h4 class="card-title">ProGides.ai - Web</h4>
                            <?php endif; ?>
                        <?php endif; ?>
                        <small class="card-subtitle">Biasanya akan membalas dalam beberapa menit</small>
                    </div>
                </div>

                <div class="card-body">
                    <div id="firstchat">
                        <form id="frmStart" action="<?= base_url('gidesai/start') ?>" method="POST" autocomplete="off">
                            <div class="form-group">
                                <label for="">Pertanyaan</label>
                                <textarea name="message" class="form-control" placeholder="Masukkan Pertanyaan yang ingin kamu sampaikan" rows="5" style="resize: none;" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">Kirim Pertanyaan</button>
                        </form>
                    </div>

                    <div class="chat-room" id="chatroom" style="display: none;">
                    </div>

                    <div id="chat-type" style="display: none;">
                    </div>
                </div>

                <div class="card-footer" id="chatfooter" style="display: none;">
                    <form id="frmReply" action="<?= base_url('gidesai/start') ?>" method="POST" autocomplete="off">
                        <div class="input-group">
                            <input type="text" name="message" class="form-control" placeholder="Masukkan pertanyaan kamu disini" required>

                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div style="display: flex; justify-content: end;">
            <div style="background-color: #fff; padding: .75rem; border-radius: 50%; box-shadow: 1px 1px 3px #888888; cursor: pointer; overflow: hidden; width: 60px; height: 60px; display: flex; justify-content: center; align-items: center;" onclick="popupAI()">
                <?php if (getPlatformName() != 'SmartDes'): ?>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" class="chat-logo" width="40">
                <?php else: ?>
                    <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="" class="chat-logo" width="40">
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.9.0/jquery.min.js"></script>
    <script src="<?= asset_url() ?>assets/dashboard/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/ckeditor/ckeditor.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/sweetalert/sweetalert.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <script src="<?= asset_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= asset_url() ?>assets/js/script.js"></script>

    <script>
        <?php if (isAdmin() && getSessionValue('MARK') == null): ?>
            $('#ModalSlider').modal('show');
        <?php endif; ?>

        // on modal slider shown
        $('#ModalSlider').on('shown.bs.modal', function() {
            $('#ModalSlider .slick-slider').slick({
                arrows: false,
                dots: false,
                infinite: true,
                speed: 300,
                slidesToShow: 1,
                adaptiveHeight: true,
                autoplay: true,
                autoplaySpeed: 5000
            });
        });

        let submenu = $('.sub-menu');

        for (let i = 0; i < submenu.length; i++) {
            $(submenu[i]).attr('data-height', $(submenu[i]).height());
            $(submenu[i]).addClass('collapsed');
        }

        $('[data-toggle="custom-toggle"]').click(function(e) {
            e.preventDefault();

            if ($(this).parent().find('.sub-menu').hasClass('collapsed')) {
                $(this).parent().find('.sub-menu').removeClass('collapsed');
                $(this).parent().find('.sub-menu').addClass('uncollapsed');
                $(this).parent().find('.sub-menu').css('height', $(this).parent().find('.sub-menu').attr('data-height'));
                $(this).parent().find('.has-arrow').addClass('rotate-arrow');
            } else {
                $(this).parent().find('.sub-menu').addClass('collapsed');
                $(this).parent().find('.sub-menu').removeClass('uncollapsed');
                $(this).parent().find('.sub-menu').css('height', '0px');
                $(this).parent().find('.has-arrow').removeClass('rotate-arrow');
            }
        });

        $('button.custom-navbar-toggler').click(function() {
            setTimeout(function() {
                $(window).trigger('resize');
            }, 1000);

            if ($('.sidebar').hasClass('hide')) {
                $('.sidebar').removeClass('hide');
                $('.content').removeClass('full');
            } else {
                $('.sidebar').addClass('hide');
                $('.content').addClass('full');
            }
        });

        $('.datatables').DataTable();

        $(document).ajaxStart(function(e) {
            $('.loading-ajax').css('opacity', 1).css('visibility', 'visible');
        });

        $(document).ajaxComplete(function(e) {
            $('.loading-ajax').css('opacity', 0).css('visibility', 'hidden');
        });

        function popupAI() {
            if ($('.chat-popup').hasClass('show')) {
                $('.chat-popup').removeClass('show');
                $('img.chat-logo').attr('src', '<?= asset_url() ?>assets/images/gides.png').attr('width', '40px');
            } else {
                $('.chat-popup').addClass('show');
                $('img.chat-logo').attr('src', '<?= asset_url() ?>assets/images/close.png').attr('width', '20px');
            }
        }

        $('#frmStart').submit(function(e) {
            e.preventDefault();

            $('#firstchat').fadeOut();
            $('#chatroom').fadeIn();

            let message = $('#firstchat textarea[name=message]').val();

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start flex-row-reverse mb-4">
                <div>
                    <img src="<?= getCurrentProfileImage() ?>" alt="" width="25" height="25" style="border-radius: 50%; object-fit: cover; object-position: center;">
                </div>

                <div class="mr-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-right-radius: 0;">
                    <span>${message}</span>
                </div>
            </div>`);

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4 typing">
                <div>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                </div>

                <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                    <div class="dot-container">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>`);

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                dataType: 'json',
                data: {
                    message: message
                },
                success: function(response) {
                    $('#chatroom .typing').remove();

                    if (response.RESULT == 'OK') {
                        $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4">
                            <div>
                                <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                            </div>

                            <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                                <span>${response.DESCRIPTION}</span>
                            </div>
                        </div>`);

                        $('#chatfooter').fadeIn();
                    }
                }
            }).fail(function() {
                $('#chatroom .typing').remove();
            });
        });

        $('#frmReply').submit(function(e) {
            e.preventDefault();

            $('#chat-type').fadeOut();

            let message = $('#frmReply input[name=message]').val();

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start flex-row-reverse mb-4">
                <div>
                    <img src="<?= getCurrentProfileImage() ?>" alt="" width="25" height="25" style="border-radius: 50%; object-fit: cover; object-position: center;">
                </div>

                <div class="mr-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-right-radius: 0;">
                    <span>${message}</span>
                </div>
            </div>`);

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4 typing">
                <div>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                </div>

                <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                    <div class="dot-container">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>`);

            $('#frmReply input[name=message]').val(null);

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                dataType: 'json',
                data: {
                    message: message
                },
                success: function(response) {
                    $('#chatroom .typing').remove();

                    if (response.RESULT == 'OK') {
                        $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4">
                            <div>
                                <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                            </div>

                            <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                                <span>${response.DESCRIPTION}</span>
                            </div>
                        </div>`);

                        $('#chatfooter').fadeIn();
                    }
                }
            }).fail(function() {
                $('#chatroom .typing').remove();
            });
        });
    </script>
</body>

</html>