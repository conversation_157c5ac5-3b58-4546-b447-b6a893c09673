<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h4 class="card-title">Detail Penerimaan BLT</h4>
                    <div class="card-tools">
                        <a href="<?= base_url('penerimaan_blt') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Info BLT -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Informasi BLT</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="30%"><strong>Deskripsi</strong></td>
                                            <td>: <?= $penerimaan_blt->deskripsi ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Nominal</strong></td>
                                            <td>: Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tanggal Dibuat</strong></td>
                                            <td>: <?= date('d/m/Y H:i', strtotime($penerimaan_blt->tanggal_dibuat)) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Statistik Penerima</h5>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="bg-primary text-white p-3 rounded">
                                                <h4><?= count($detail_penerima) ?></h4>
                                                <small>Total Penerima</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="bg-success text-white p-3 rounded">
                                                <h4><?= count(array_filter($detail_penerima, function ($item) {
                                                        return $item->status_verifikasi == 'terverifikasi';
                                                    })) ?></h4>
                                                <small>Terverifikasi</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="bg-warning text-white p-3 rounded">
                                                <h4><?= count(array_filter($detail_penerima, function ($item) {
                                                        return $item->status_verifikasi == 'menunggu_verifikasi';
                                                    })) ?></h4>
                                                <small>Menunggu</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Daftar Penerima -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Daftar Penerima BLT</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-detail-penerima" class="table table-striped table-bordered" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>NIK</th>
                                            <th>Nama</th>
                                            <th>Alamat</th>
                                            <th>RT/RW</th>
                                            <th>Status</th>
                                            <th>Tanggal Verifikasi</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $no = 1;
                                        foreach ($detail_penerima as $penerima): ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $penerima->nik ?></td>
                                                <td><?= $penerima->nama ?></td>
                                                <td><?= $penerima->alamat ?></td>
                                                <td><?= $penerima->rt ?>/<?= $penerima->rw ?></td>
                                                <td>
                                                    <?php if ($penerima->status_verifikasi == 'terverifikasi'): ?>
                                                        <span class="badge badge-success">Terverifikasi</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">Menunggu Verifikasi</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= $penerima->tanggal_verifikasi ? date('d/m/Y H:i', strtotime($penerima->tanggal_verifikasi)) : '-' ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <?php if ($penerima->status_verifikasi == 'menunggu_verifikasi'): ?>
                                                            <button type="button" class="btn btn-success btn-sm btn-verifikasi"
                                                                data-id="<?= $penerima->id ?>"
                                                                data-status="terverifikasi"
                                                                data-nama="<?= $penerima->nama ?>"
                                                                title="Verifikasi">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-warning btn-sm btn-verifikasi"
                                                                data-id="<?= $penerima->id ?>"
                                                                data-status="menunggu_verifikasi"
                                                                data-nama="<?= $penerima->nama ?>"
                                                                title="Batalkan Verifikasi">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Verifikasi -->
<div class="modal fade" id="modal-verifikasi" tabindex="-1" role="dialog" aria-labelledby="modal-verifikasi-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-verifikasi-label">Konfirmasi Verifikasi</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="verifikasi-message"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="btn-confirm-verifikasi">Konfirmasi</button>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        // Initialize DataTable
        $('#table-detail-penerima').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            },
            "order": [
                [1, "asc"]
            ]
        });

        // Verifikasi functionality
        var verifikasiData = {};

        $(document).on('click', '.btn-verifikasi', function() {
            verifikasiData = {
                id: $(this).data('id'),
                status: $(this).data('status'),
                nama: $(this).data('nama')
            };

            var message = '';
            if (verifikasiData.status === 'terverifikasi') {
                message = `Apakah Anda yakin ingin memverifikasi bahwa <strong>${verifikasiData.nama}</strong> telah menerima BLT?`;
                $('#btn-confirm-verifikasi').removeClass('btn-warning').addClass('btn-success').text('Verifikasi');
            } else {
                message = `Apakah Anda yakin ingin membatalkan verifikasi untuk <strong>${verifikasiData.nama}</strong>?`;
                $('#btn-confirm-verifikasi').removeClass('btn-success').addClass('btn-warning').text('Batalkan');
            }

            $('#verifikasi-message').html(message);
            $('#modal-verifikasi').modal('show');
        });

        $('#btn-confirm-verifikasi').click(function() {
            if (verifikasiData.id) {
                $.ajax({
                    url: '<?= base_url('penerimaan_blt/update_verifikasi') ?>',
                    type: 'POST',
                    data: {
                        id_detail: verifikasiData.id,
                        status: verifikasiData.status
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        $('#btn-confirm-verifikasi').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Memproses...');
                    },
                    success: function(response) {
                        $('#modal-verifikasi').modal('hide');
                        if (response.RESULT === 'OK') {
                            swal("Berhasil!", response.MESSAGE, "success", {
                                timer: 1500
                            });
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            swal("Gagal!", response.MESSAGE, "error");
                        }
                    },
                    error: function() {
                        $('#modal-verifikasi').modal('hide');
                        swal("Error!", "Terjadi kesalahan saat memproses verifikasi", "error");
                    },
                    complete: function() {
                        $('#btn-confirm-verifikasi').prop('disabled', false);
                    }
                });
            }
        });
    }
</script>

<style>
    .card-tools {
        margin-left: auto;
    }

    .btn-group .btn {
        margin-right: 2px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    .bg-primary,
    .bg-success,
    .bg-warning {
        border-radius: 8px;
    }

    .table-borderless td {
        border: none;
        padding: 0.25rem 0.75rem;
    }
</style>