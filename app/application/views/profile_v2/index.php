<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?= isset($setting->desa) ? 'Desa ' . $setting->desa : 'Go Digital Desa' ?></title>
    <meta name="description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
    <meta name="keywords" content="<?= isset($setting->keywords) ? $setting->keywords : 'platform desa digital, portal desa digital, aplikasi digital desa, desa digital adalah, bip desa, digital desa, digital desa online, aplikasi pelayanan desa online, kriteria desa digital, layanan desa, login desa digital, web pelayanan desa, online digital desa, profil desa digital, rab desa digital, smart desa digital, download portal desa digital, website desa online, cara membuat desa digital, program desa digital 2021, contoh program desa digital, membangun desa digital, sistem desa digital, desa digital di indonesia, program desa digital, proposal desa digital, desa wisata digital adalah, aplikasi desa digital, apa itu desa digital, anggaran desa digital, ayo bangun desa digital, artikel desa digital, arti desa digital, apa keuntungan kelemahan peluang dan ancaman dalam menerapkan desa digital, desa berbasis digital, desa wisata berbasis digital, bumdes desa digital, contoh desa digital, ciri ciri desa digital, cara login desa digital, desa cerdas digital, ekonomi digital desa, gambar desa digital, inovasi desa digital, jumlah desa digital di indonesia, daftar desa digital di indonesia, konsep desa digital, kelebihan dan kekurangan desa digital, kriteria desa digital, kategori desa digital, desa digital login, login page desa digital, latar belakang desa digital, logo desa digital, manfaat desa digital, membangun desa digital, maksud dan tujuan desa digital, memajukan pariwisata desa melalui media digital, sistem desa digital, syarat menjadi desa digital, sistem informasi desa digital, sosialisasi desa digital, desa digital terbaik, template desa digital, target desa digital, teknologi desa digital, website desa digital, desa wisata digital di indonesia desa digital 2022, program digitalisasi desa, surat desa otomatis, aplikasi surat menyurat desa otomatis, format surat desa, kode surat desa, kop surat desa doc, logo surat desa, penomoran surat desa, nomor surat otomatis, surat menyurat desa excel, tata naskah surat desa, nomor surat tugas desa, nomor agenda surat desa' ?>">
    <meta name="robots" content="index, follow">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <!-- Open Graph / Facebook -->
    <meta property="og:site_name" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= base_url() ?>">
    <meta property="og:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
    <meta property="og:image" content="">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?= base_url() ?>">
    <meta property="twitter:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="twitter:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
    <meta property="twitter:image" content="">

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/style.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/libs/slick-1.8.1/slick/slick.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/libs/slick-1.8.1/slick/slick-theme.css">

    <link rel="canonical" href="<?= base_url(uri_string()) ?>">

    <style>
        #aspirasi .aspirasi {
            background-color: #007755 !important;
        }

        #aspirasi .aspirasi .form-group label {
            color: white;
        }

        #kepdes .learnmore {
            margin-top: 1rem;
            background-color: #007755;
            color: white;
            float: right;
            padding: 12px 40px;
            border-radius: 25px;
        }

        #kepdes .slick-list {
            overflow: unset;
        }

        #aspirasi .btn.submit {
            background-color: orange;
        }

        .navbar .navbar-nav .nav-item {
            padding-left: 5px;
            padding-right: 5px;
        }

        .carousel,
        .carousel-item {
            height: 90vh;
        }

        .carousel-item .carousel-content {
            position: absolute;
            top: 0;
            left: 5rem;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: flex-end;
            color: #fff;
            padding: 60px 0;
        }

        .carousel-item p {
            width: 100%;
        }

        .carousel-item h5 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0;
            max-width: 80%;
        }

        .carousel-content-button {
            margin-top: 15px;
        }

        .carousel-inner {
            height: 100%;
        }

        .carousel-inner .carousel-item>img {
            -webkit-animation: zoom 20s;
            animation: zoom 20s;
        }

        .sitelink ul li a {
            color: white;
            text-decoration: none;
        }

        #map {
            height: 400px;
            width: 100%;
        }

        @-webkit-keyframes zoom {
            from {
                -webkit-transform: scale(1, 1);
            }

            to {
                -webkit-transform: scale(1.5, 1.5);
            }
        }

        @keyframes zoom {
            from {
                transform: scale(1, 1);
            }

            to {
                transform: scale(1.5, 1.5);
            }
        }

        @media screen and (max-width: 768px) {
            .carousel-item .carousel-content {
                left: 2.5rem;
            }
        }

        @media screen and (max-width: 576px) {
            .carousel-item h5 {
                font-size: 1.2rem;
                max-width: 100%;
            }

            .carousel-item .carousel-content {
                left: 1rem;
                right: 1rem;
                text-align: center;
                width: unset;
            }
        }
    </style>

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "GovernmentOrganization",
            "name": "<?= isset($setting->desa) ? $setting->desa : null ?>",
            "alternateName": "Go Digital Desa",
            "url": "<?= base_url() ?>",
            "logo": "https://gides.id/assets/img/favicon.png"
        }
    </script>

    <script type=“text/javascript” src=“https://platform-api.sharethis.com/js/sharethis.js#property=647181d97cccdc001910bb74&product=inline-share-buttons” async=“async”></script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2417204464444609" crossorigin="anonymous"></script>

    <?php if (getPlatformName() == 'Gides') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-495ZLQJLPL"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-495ZLQJLPL');
        </script>
    <?php elseif (getPlatformName() == 'GidesManis') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-YYSG07EWM1"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-YYSG07EWM1');
        </script>
    <?php endif; ?>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <div class="d-flex flex-row">
                    <div>
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img class="img-logo" width="50" src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" />
                        <?php endif; ?>
                    </div>

                    <div class="ml-2">
                        <p class="nama-desa mb-0" style="font-size: 1.25rem; color: #E8EF4D; font-weight: 700;"><?= isset($setting->desa) ? $setting->desa : null ?></p>
                        <p class="kab-desa mb-0" style="font-size: 1rem;"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></p>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item active">
                        <a class="nav-link" href="<?= base_url('pemerintah') ?>">Pemerintahan</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('infografi') ?>">Infografis <span class="badge badge-danger" style="right: -10px;">New</span></a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('belanja') ?>">Belanja</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('berita/all') ?>">Berita <span class="badge badge-danger" style="right: -10px;">New</span></a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('surat') ?>">Surat</a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-expanded="false">
                            Lainnya
                        </a>

                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="<?= base_url('bantuan-sosial') ?>">
                                <i class="fas fa-hands-helping me-2"></i>Bantuan Sosial
                            </a>
                            <a class="dropdown-item" href="<?= base_url('auth/login') ?>">Login CMS</a>
                            <a class="dropdown-item" href="<?= base_url('pengaduan') ?>">Pengaduan Masyarakat</a>
                            <a class="dropdown-item" href="<?= base_url('guestbook') ?>">Buku Tamu Digital</a>
                            <div class="dropdown-divider"></div>
                            <?php foreach ($this->db->get_where('highlight', array('id_user' => $setting->id_user))->result() as $key => $value) : ?>
                                <a href="<?= base_url('highlight/content/' . $value->id) ?>" class="dropdown-item"><?= $value->judul ?></a>
                            <?php endforeach; ?>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <style>
        .carousel .feature-list {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .carousel .feature-list small {
            font-size: .75rem;
        }

        .carousel .feature-list h1 {
            font-size: 28px;
        }

        .carousel .feature-list h2 {
            font-size: 24px;
        }

        .carousel .feature-list img {
            transition: .3s;
        }

        .carousel .feature-list .card .card-body:hover img {
            transform: scale(1.1);
        }
    </style>

    <div class="jumbotron jumbotron-fluid pt-0 pb-0">
        <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">
            <div class="feature-list">
                <div class="d-flex justify-content-center align-items-center h-100">
                    <div class="container">
                        <div class="text-center mb-5">
                            <h1 id="typewriters" class="text-white">SISTEM INFORMASI <?= strtoupper($setting->desa ?? '-') ?></h1>
                            <h2 class="text-white"><?= $setting->kabupaten ?? '-' ?></h2>
                        </div>

                        <div class="row">
                            <div class="col">
                                <a href="<?= base_url('produk/hukum') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/compliant.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Produk Hukum</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('belanja') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/trolley.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Belanja</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('berita/all') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/archive.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Berita</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('infografi') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/pie-chart.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Infografis</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('surat') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/letter.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Surat</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('guestbook') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/guestbook.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Buku Tamu</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('pengelolaandana') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/survey.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Anggaran</small>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="col">
                                <a href="<?= base_url('pemerintah') ?>" class="text-decoration-none">
                                    <div class="card">
                                        <div class="card-body d-flex flex-column align-items-center text-center">
                                            <img src="<?= base_url('assets/images/government.png') ?>" alt="" width="35" style="height: unset;">
                                            <small class="mt-2">Pemerintahan</small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <ol class="carousel-indicators">
                <?php if (count($slider) == 0 && count($slider_berita) == 0) : ?>
                    <li data-target="#carouselExampleControls" data-slide-to="0" class="active"></li>
                <?php else : ?>
                    <?php $slider_key = 0; ?>
                    <?php foreach ($slider as $key => $value) : ?>
                        <li data-target="#carouselExampleControls" data-slide-to="<?= $key ?>" class="<?= $key == 0 ? 'active' : null ?>"></li>

                        <?php $slider_key = $key; ?>
                    <?php endforeach; ?>

                    <?php foreach ($slider_berita as $key => $value) : ?>
                        <li data-target="#carouselExampleControls" data-slide-to="<?= ++$slider_key ?>"></li>
                    <?php endforeach; ?>
                <?php endif; ?>
            </ol>

            <div class="carousel-inner">
                <?php if (count($slider) == 0 && count($slider_berita) == 0) : ?>
                    <div class="carousel-item active">
                        <img src="<?= asset_url() ?>assets/templatesv2/img/jumbotron.png" alt="Gambar Jumbotron" style="width: 100%; height: 100%; object-fit: cover; object-position: center;">
                    </div>
                <?php else : ?>
                    <?php foreach ($slider as $key => $value) : ?>
                        <div class="carousel-item <?= $key == 0 ? 'active' : null ?>">
                            <?php if ($value->slidertype == 'Image') : ?>
                                <img src="<?= asset_url($value->image_slider) ?>" alt="Gambar Slider" style="width: 100%; height: 100%; object-fit: cover; object-position: center;">
                            <?php else : ?>
                                <div data-vbg="<?= $value->video_slider ?>"></div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>

                    <?php foreach ($slider_berita as $key => $value) : ?>
                        <div class="carousel-item">
                            <img src="<?= asset_url($value->foto) ?>" alt="Gambar Slider" style="width: 100%; height: 100%; object-fit: cover; object-position: center;">

                            <!-- <div class="carousel-content">
                                <div>
                                    <div class="carousel-content-title">
                                        <h5><?= $value->judul ?></h5>
                                    </div>

                                    <div class="carousel-content-button">
                                        <a href="<?= base_url('berita/' . DateFormat($value->createddate, 'Y')) . '/' . $value->link_access ?>?id=<?= $value->id ?>" class="btn btn-primary">Baca Selengkapnya</a>
                                    </div>
                                </div>
                            </div> -->
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div id="kepdes" class="mt-5">
        <div class="container">
            <div class="text-center">
                <?php if ($user->id != 893): ?>
                    <h3>Sambutan <?= $user->admintype == 'Kelurahan' ? 'Lurah' : ($user->admintype == 'Kecamatan' ? 'Camat' : 'Kepala Desa') ?></h3>
                <?php else: ?>
                    <h3>Profil Calon Ketua LPM Kelurahan Syamsudin Noor</h3>
                <?php endif; ?>
            </div>

            <div class="so-slider">
                <div class="so-item">
                    <div>
                        <div class="so-picture">
                            <img src="<?= asset_url($setting->foto) ?>" alt="Gambar Kepala Desa" width="100%">
                        </div>
                    </div>

                    <div>
                        <div class="so-detail">
                            <div class="bg-green">
                                <?php if (isset($setting->isi) && $setting->isi != null) : ?>
                                    <p><?= $setting->isi ?></p>
                                <?php else : ?>
                                    <?php if ($user->admintype == 'Kelurahan') : ?>
                                        <p>Selamat datang di website resmi Kelurahan kami.</p>
                                        <p>Sebagai Lurah, saya sangat bangga dan senang dapat menyambut Anda di sini, Kelurahan Kami merupakan kelurahan yang dikenal dengan keramahan dan kebersamaan warga, Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan kelurahan yang lebih baik dan sejahtera bagi generasi masa depan.</p>
                                        <p>Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan berkoordinasi dengan pemerintah kelurahan.</p>
                                        <p>Terima kasih atas kunjungan Anda&nbsp; dan semoga website ini dapat memberikan manfaat bagi Anda semua.</p>
                                        <p>Salam hangat</p>
                                    <?php else : ?>
                                        <p>Selamat datang di website resmi Desa kami.</p>
                                        <p>Sebagai Kepala Desa, saya sangat bangga dan senang dapat menyambut Anda di sini, Desa Kami merupakan desa yang dikenal dengan keramahan dan kebersamaan warga, Kami percaya bahwa dengan bekerja sama, kita dapat menciptakan desa yang lebih baik dan sejahtera bagi generasi masa depan.</p>
                                        <p>Dengan adanya website ini, kami berharap dapat memberikan informasi yang berguna dan akurat bagi masyarakat, serta mempermudah akses bagi masyarakat untuk berinteraksi dan berkoordinasi dengan pemerintah desa.</p>
                                        <p>Terima kasih atas kunjungan Anda&nbsp; dan semoga website ini dapat memberikan manfaat bagi Anda semua.</p>
                                        <p>Salam hangat</p>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <div class="profile">
                                    <div class="profile-name"><?= isset($setting->nama) ? $setting->nama : null ?></div>
                                    <?php if ($user->id != 893): ?>
                                        <div class="profile-position"><?= $user->admintype == 'Kelurahan' ? 'Lurah' : ($user->admintype == 'Kecamatan' ? 'Camat' : 'Kepala Desa') ?></div>
                                    <?php else: ?>
                                        <div class="profile-position">Calon Ketua LPM Syamsudin Noor</div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <button type="button" class="btn learnmore" onclick="window.location.href = '<?= base_url('pemerintah') ?>';">Selengkapnya</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($coords != null) : ?>
        <div id="map-container" class="content" style="margin-top: 6rem; margin-bottom: 0rem;">
            <div class="container">
                <div class="text-center">
                    <p class="header">Batas <?= $user->admintype == 'Kelurahan' ? 'Lurah' : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?></p>
                    <h2><?= isset($setting->desa) ? $setting->desa : null ?></h2>
                </div>

                <div class="card" style="border: none;">
                    <div class="card-body">
                        <div id="map"></div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div id="pemerintahan" class="content" style="margin-top: -3rem;">
        <div class="container">
            <p class="header">Pemerintahan</p>
            <h2>Program <?= $user->admintype == 'Kelurahan' ? 'Kelurahan' : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Pemerintah Desa') ?></h2>

            <div class="row">
                <div class="col-lg-6">
                    <div class="text-center">
                        <img src="https://storage.googleapis.com/gides/interior2.png" alt="Gambar Desain Pemerintahan Desa" width="75%">
                    </div>
                </div>

                <div class="col-lg-6">
                    <h4>Sekilas Informasi Mengenai<br>Program <?= $user->admintype == 'Kelurahan' ? 'Kerja Lurah' : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?>.</h4>
                    <!-- <p class="description">Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                        Lorem Ipsum has been
                        the industry's</p> -->

                    <div class="custom-list">
                        <ul>
                            <li>
                                <div class="circle"></div>
                                <div>
                                    Penyelenggaraan Pemerintahan <?= $user->admintype == 'Kelurahan' ? null : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?>
                                    <p>Masyarakat Yang Adil dan Sejahtra</p>
                                </div>
                            </li>

                            <li>
                                <div class="circle"></div>
                                <div>
                                    Pelaksanaan Pembangunan <?= $user->admintype == 'Kelurahan' ? null : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?>
                                    <p>Masyarakat Yang Adil dan Sejahtra</p>
                                </div>
                            </li>

                            <li>
                                <div class="circle"></div>
                                <div>
                                    Pembinaan Kemasyarakatan <?= $user->admintype == 'Kelurahan' ? null : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?>
                                    <p>Masyarakat Yang Adil dan Sejahtra</p>
                                </div>
                            </li>

                            <li>
                                <div class="circle"></div>
                                <div>
                                    Pemberdayaan Masyarakat <?= $user->admintype == 'Kelurahan' ? null : ($user->admintype == 'Kecamatan' ? 'Kecamatan' : 'Desa') ?>
                                    <p>Masyarakat Yang Adil dan Sejahtra</p>
                                </div>
                            </li>

                            <li>
                                <div class="circle"></div>
                                <div>
                                    Penanggulangan Bencana
                                    <p>Masyarakat Yang Adil dan Sejahtra</p>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <div class="d-flex button-group">
                        <button type="button" class="btn learnmore" onclick="window.location.href = '<?= base_url('pengelolaandana') ?>';">Lihat Detail</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="infografis" class="content">
        <div class="container">
            <p class="header">Infografis</p>
            <h2>Infografis <?= $setting->desa ?></h2>

            <div class="infografis mt-4">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/management.svg" alt="Gambar Luas Kas Desa" class="mr-3" width="50">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Luas Kas</p>
                                    <h5><?= isset($luas_tanah->luas_tanah_kas) ? IDR($luas_tanah->luas_tanah_kas) : 0 ?>m<sup>2</sup></h5>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/land.svg" alt="Gambar Luas Tanah" class="mr-3" width="50">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Luas Tanah</p>
                                    <h5><?= isset($luas_tanah->luas_tanah_desa) ? IDR($luas_tanah->luas_tanah_desa) : 0 ?>m<sup>2</sup></h5>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/architect.svg" alt="Gambar Luas DHKP" class="mr-3" width="50">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Luas DHKP</p>
                                    <h5><?= isset($luas_tanah->luas_dhkp) ? IDR($luas_tanah->luas_dhkp) : 0 ?>m<sup>2</sup></h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="divider mt-5 mb-5"></div>

                    <div class="row">
                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/people.png" alt="Gambar Jumlah Penduduk" class="mr-3" width="50" style="background-color: white; border-radius: 50%; padding: .75rem;">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Jumlah Penduduk</p>
                                    <h5><?= IDR((isset($infografis->laki) ? $infografis->laki : 0) + (isset($infografis->perempuan) ? $infografis->perempuan : 0)) ?></h5>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/user-minus.png" alt="Gambar Laki Laki" class="mr-3" width="50" style="background-color: white; border-radius: 50%; padding: .75rem;">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Laki Laki</p>
                                    <h5><?= isset($infografis->laki) ? IDR($infografis->laki) : 0 ?></h5>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="infografis-detail">
                                <div>
                                    <img src="<?= asset_url() ?>assets/templatesv2/img/user-tick.png" alt="Gambar Perempuan" class="mr-3" width="50" style="background-color: white; border-radius: 50%; padding: .75rem;">
                                </div>

                                <div class="d-flex flex-column">
                                    <p class="mb-0">Perempuan</p>
                                    <h5><?= isset($infografis->perempuan) ? IDR($infografis->perempuan) : 0 ?></h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-5">
                        <p class="information">Kunjungi Informasi Tentang Infografis</p>

                        <button type="button" class="btn learnmore" onclick="window.location.href = '<?= base_url('infografi') ?>';">Selengkapnya</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="belanja" class="content">
        <div class="container">
            <div class="d-flex flex-row justify-content-between align-items-center">
                <div>
                    <p class="header">Belanja</p>
                    <h2>Produk dan Kuliner</h2>
                </div>

                <div class="d-flex flex-column align-items-end">
                    <div>
                        <a href="<?= base_url('belanja') ?>" class="text-decoration-none" style="color: #007755;">Selengkapnya</a>
                    </div>

                    <div class="d-flex flex-row mt-1">
                        <button type="button" class="btn slider-button mr-1" onclick="prev()">
                            <i class="fa fa-arrow-left"></i>
                        </button>

                        <button type="button" class="btn slider-button" onclick="next()">
                            <i class="fa fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="product-slider">
                <?php if (count($produk) >= 3) : ?>
                    <?php foreach ($produk as $key => $value) : ?>
                        <div class="product-item">
                            <a href="<?= base_url('guest/product_detail/' . $value->id) ?>" class="text-decoration-none">
                                <div class="product-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Produk" width="100%">

                                    <span class="label">Populer</span>
                                </div>
                            </a>

                            <div class="product-detail">
                                <div>
                                    <p class="product-title"><?= $value->nama ?></p>

                                    <button type="button" class="btn buy" onclick="pesan('<?= $value->no_telp ?>')">Beli</button>
                                </div>

                                <a href="javascript:;" class="active">
                                    <i class="fa fa-heart"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php elseif (count($produk) > 0) : ?>
                    <?php $calculate = 3 - count($produk); ?>

                    <?php foreach ($produk as $key => $value) : ?>
                        <div class="product-item">
                            <a href="<?= base_url('guest/product_detail/' . $value->id) ?>" class="text-decoration-none">
                                <div class="product-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Produk" width="100%">

                                    <span class="label">Populer</span>
                                </div>
                            </a>

                            <div class="product-detail">
                                <div>
                                    <p class="product-title"><?= $value->nama ?></p>

                                    <button type="button" class="btn buy" onclick="pesan('<?= $value->no_telp ?>')">Beli</button>
                                </div>

                                <a href="javascript:;" class="active">
                                    <i class="fa fa-heart"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <?php for ($i = 0; $i < $calculate; $i++) { ?>
                        <div class="product-item">
                            <a href="<?= base_url('guest/product_detail/' . $value->id) ?>" class="text-decoration-none">
                                <div class="product-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Produk" width="100%">

                                    <span class="label">Populer</span>
                                </div>
                            </a>

                            <div class="product-detail">
                                <div>
                                    <p class="product-title"><?= $value->nama ?></p>

                                    <button type="button" class="btn buy" onclick="pesan('<?= $value->no_telp ?>')">Beli</button>
                                </div>

                                <a href="javascript:;" class="active">
                                    <i class="fa fa-heart"></i>
                                </a>
                            </div>
                        </div>
                    <?php } ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div id="berita" class="content">
        <div class="container">
            <div class="d-flex flex-row justify-content-between align-items-center">
                <div>
                    <p class="header">Berita</p>
                    <h2>Informasi dan Kabar Berita</h2>
                </div>

                <div class="d-flex">
                    <button type="button" class="btn slider-button mr-1" onclick="prev('berita')">
                        <i class="fa fa-arrow-left"></i>
                    </button>

                    <button type="button" class="btn slider-button" onclick="next('berita')">
                        <i class="fa fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <div class="berita-slider">
                <?php if (count($berita) >= 3) : ?>
                    <?php foreach ($berita as $key => $value) : ?>
                        <div class="berita-item">
                            <a href="<?= base_url('berita/' . DateFormat($value->createddate, 'Y')) . '/' . $value->link_access ?>?id=<?= $value->id ?>" class="text-decoration-none">
                                <div class=" berita-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Berita" width="100%">
                                </div>

                                <div class="berita-detail">
                                    <p class="berita-title" style="overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;"><?= $value->judul ?></p>

                                    <div class="d-flex justify-content-between w-100 mt-3">
                                        <p class="berita-date"><?= DateFormat($value->tanggal, 'd F Y') ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php elseif (count($berita) > 0) : ?>
                    <?php $calculate = 3 - count($berita); ?>

                    <?php foreach ($berita as $key => $value) : ?>
                        <div class="berita-item">
                            <a href="<?= base_url('berita/' . DateFormat($value->createddate, 'Y')) . '/' . $value->link_access ?>?id=<?= $value->id ?>" class="text-decoration-none">
                                <div class="berita-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Berita" width="100%">
                                </div>

                                <div class="berita-detail">
                                    <p class="berita-title" style="overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;"><?= $value->judul ?></p>

                                    <div class="d-flex justify-content-between w-100 mt-3">
                                        <p class="berita-date"><?= DateFormat($value->tanggal, 'd F Y') ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>

                    <?php for ($i = 0; $i < $calculate; $i++) : ?>
                        <div class="berita-item">
                            <a href="<?= base_url('berita/' . DateFormat($value->createddate, 'Y')) . '/' . $value->link_access ?>?id=<?= $value->id ?>" class="text-decoration-none">
                                <div class=" berita-image">
                                    <img src="<?= asset_url($value->foto) ?>" alt="Gambar Berita" width="100%">
                                </div>

                                <div class="berita-detail">
                                    <p class="berita-title" style="overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;"><?= $value->judul ?></p>

                                    <div class="d-flex justify-content-between w-100 mt-3">
                                        <p class="berita-date"><?= DateFormat($value->tanggal, 'd F Y') ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endfor; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div id="wisata" class="content pb-0">
        <div class="container">
            <div class="d-flex flex-row justify-content-between align-items-center">
                <div>
                    <p class="header">Wisata</p>
                    <h2>Tempat Wisata</h2>
                </div>

                <div class="d-flex">
                    <button type="button" class="btn slider-button mr-1" onclick="prev('wisata')">
                        <i class="fa fa-arrow-left"></i>
                    </button>

                    <button type="button" class="btn slider-button" onclick="next('wisata')">
                        <i class="fa fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <div class="wisata-slider">
                <?php if (count($wisata) >= 3) : ?>
                    <?php foreach ($wisata as $key => $value) : ?>
                        <?php $exploding = explode(',', $value->foto); ?>

                        <div class="wisata-item">
                            <a href="<?= base_url('wisata/' . $value->id) ?>">
                                <div class="wisata-image">
                                    <img src="<?= asset_url($exploding[0]) ?>" alt="Gambar Wisata" width="100%">

                                    <div class="wisata-detail">
                                        <p class="wisata-title"><?= $value->judul ?></p>
                                        <p class="wisata-description"><?= $value->sub_judul ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php elseif (count($wisata) > 0) : ?>
                    <?php $calculate = 3 - count($wisata); ?>

                    <?php foreach ($wisata as $key => $value) : ?>
                        <?php $exploding = explode(',', $value->foto); ?>

                        <div class="wisata-item">
                            <a href="<?= base_url('wisata/' . $value->id) ?>">
                                <div class="wisata-image">
                                    <img src="<?= asset_url() ?>assets/images/wisata/<?= $exploding[0] ?>" alt="Gambar Wisata" width="100%">

                                    <div class="wisata-detail">
                                        <p class="wisata-title"><?= $value->judul ?></p>
                                        <p class="wisata-description"><?= $value->sub_judul ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>

                    <?php for ($i = 0; $i < $calculate; $i++) : ?>
                        <div class="wisata-item">
                            <a href="<?= base_url('wisata/' . $value->id) ?>">
                                <div class="wisata-image">
                                    <img src="<?= asset_url() ?>assets/images/wisata/<?= $exploding[0] ?>" alt="Gambar Wisata" width="100%">

                                    <div class="wisata-detail">
                                        <p class="wisata-title"><?= $value->judul ?></p>
                                        <p class="wisata-description"><?= $value->sub_judul ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endfor; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div id="aspirasi" class="content">
        <div class="container">
            <p class="header">Aspirasi</p>
            <h2>Aspirasi/Laporan Pengaduan Masyarakat</h2>

            <div class="aspirasi">
                <form id="frmKritikSaran" action="<?= base_url(uri_string() . '/kritiksaran') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-lg-6" style="position: relative;">
                            <div class="form-group">
                                <label class="ml-0">Kategori Pengaduan</label>
                                <select name="bidangpengaduan" class="form-control" required>
                                    <option value="">- Pilih -</option>
                                    <?php foreach ($bidang as $key => $value) : ?>
                                        <option value="<?= $value->id ?>"><?= $value->name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="ml-0">NIK</label>
                                <p style="color: #E8EF4D;" class="small">*Identitas pelapor akan dirahasiakan</p>
                                <input type="text" name="nik" class="form-control" placeholder="Masukkan NIK Anda">
                            </div>

                            <div class="form-group">
                                <label class="ml-0">Foto</label>
                                <input type="file" name="foto" class="form-control" required>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group">
                                <label>Tulis Pesan</label>
                                <textarea name="pesan" class="form-control" rows="10" placeholder="Masukkan Isi Pesan Anda"></textarea>
                            </div>

                            <div class="text-right mt-4">
                                <button type="submit" class="btn submit">Kirim Pesan</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex flex-row item">
                        <div>
                            <?php if (isset($setting->logo_desa)) : ?>
                                <img class="img-logo" width="50" src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" />
                            <?php endif; ?>
                        </div>

                        <div class="ml-3">
                            <p class="nama-desa mb-0" style="font-size: 20px; color: #E8EF4D; font-weight: 700;"><?= isset($setting->desa) ? $setting->desa : null ?></p>
                            <p class="kab-desa mb-0" style="font-size: 16px;"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="item">
                        <p class="header">Alamat</p>
                        <p class="detail alamat"><?= isset($setting->alamat) ? $setting->alamat : null ?></p>

                        <p class="header">Kontak Penting</p>
                        <?php foreach ($kontakpenting as $key => $value) : ?>
                            <p class="detail"><?= $value->nama ?>: <a href="tel:<?= $value->no_telp ?>" class="text-decoration-none text-white"><?= $value->no_telp ?></a></p>
                        <?php endforeach; ?>

                        <p class="detail mt-1" id="email_protection"><?= isset($setting->email) ? base64_encode($setting->email) : null ?></p>
                    </div>
                </div>

                <div class=" col-md-4">
                    <div class="item">
                        <p class="header">Sitemap</p>

                        <div class="sitelink">
                            <ul>
                                <li>
                                    <a href="<?= base_url('pemerintah') ?>" class="text-decoration-none text-white">
                                        Pemerintahan
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('infografi') ?>" class="text-decoration-none text-white">
                                        Infografis
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('belanja') ?>" class="text-decoration-none text-white">
                                        Belanja
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('berita/all') ?>" class="text-decoration-none text-white">
                                        Berita
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('surat') ?>" class="text-decoration-none text-white">
                                        Surat
                                    </a>
                                </li>
                            </ul>

                            <ul>
                                <li>
                                    <a href="<?= isset($setting->facebook) ? validate_sosmedurl($setting->facebook, 'facebook') : 'javascript:;' ?>" target="_blank">Facebok</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->twitter) ? validate_sosmedurl($setting->twitter, 'twitter') : 'javascript:;' ?>" target="_blank">Twitter</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->youtube) ? validate_sosmedurl($setting->youtube, 'youtube') : 'javascript:;' ?>" target="_blank">Youtube</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->instagram) ? validate_sosmedurl($setting->instagram, 'instagram') : 'javascript:;' ?>" target="_blank">Instagram</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 text-center">
                    <p class="copyright">&copy; 2022 Go Digital Desa (<?= strtoupper(getPlatformName()) ?>). All Rights reserved</p>
                </div>

                <!-- <div class="item">
                <div class="top">
                    <i class="fa fa-arrow-up"></i>
                </div>
                </div> -->
            </div>
        </div>
    </footer>

    <div class="modal fade" id="hubungi_penjual" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Hubungi Penjual</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="card" style="cursor: pointer;" id="card_hubungi_telepon">
                        <div class="card-body">
                            <div class="float-left">
                                <img src="<?= asset_url() ?>assets/templates/images/telephone.png" alt="Gambar Telepon" width="30" style="width: 30 !important">
                            </div>

                            <div style="margin-left: 4rem;">
                                <div>Telepon</div>
                                <span id="telepon_penjual"></span>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>

                    <div class="card mt-2" style="cursor: pointer;" id="card_hubungi_whatsapp">
                        <div class="card-body">
                            <div class="float-left">
                                <img src="<?= asset_url() ?>assets/templates/images/whatsapp.png" alt="Gambar WhatsApp" width="50">
                            </div>

                            <div style="margin-left: 4rem;">
                                <div>Whatsapp</div>
                                <span id="whatsapp_penjual"></span>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="<?= asset_url() ?>assets/templates/js/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.12.9/dist/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="<?= asset_url() ?>assets/templatesv2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/js/all.min.js" integrity="sha512-6PM0qYu5KExuNcKt5bURAoT6KCThUmHRewN3zUFNaoI6Di7XJPTMoT6K0nsagZKk2OB4L7E3q1uQKHNHd4stIQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="<?= asset_url() ?>assets/templatesv2/libs/slick-1.8.1/slick/slick.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/sweetalert/sweetalert.min.js"></script>
    <script src="<?= asset_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= asset_url() ?>assets/js/script.js"></script>
    <script type="text/javascript" src="https://unpkg.com/youtube-background/jquery.youtube-background.min.js"></script>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBwylf4eX5wOPvQEZshnLCIz5M7u2VvgC4" async defer></script>
    <script src="https://unpkg.com/typewriter-effect@latest/dist/core.js"></script>

    <script>
        var typewriter = new Typewriter(document.getElementById('typewriters'), {
            loop: true,
            delay: 75,
            autoStart: true,
        });

        typewriter.typeString('SISTEM INFORMASI <?= strtoupper($setting->desa ?? '-') ?>')
            .pauseFor(2500)
            .deleteAll()
            .start();
    </script>

    <script>
        $('.carousel').carousel({
            pause: false
        });
        $('.jumbotron-slider').slick();

        $('.so-slider').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 2000,
            dots: true,
            arrows: false
        });

        $('.product-slider').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            arrows: false,
            autoplaySpeed: 2000,
            responsive: [{
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 576,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });

        $('.berita-slider').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            arrows: false,
            autoplaySpeed: 2000,
            responsive: [{
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 576,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });

        $('.wisata-slider').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            arrows: false,
            autoplaySpeed: 2000,
            responsive: [{
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 576,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });

        function prev(type = null) {
            if (type != null) {
                $(`.${type}-slider`).slick('slickPrev');
            } else {
                $('.product-slider').slick('slickPrev');
            }
        }

        function next(type = null) {
            if (type != null) {
                $(`.${type}-slider`).slick('slickNext');
            } else {
                $('.product-slider').slick('slickNext');
            }
        }

        $(window).scroll(function() {
            var scroll = $(window).scrollTop();

            if (scroll >= 200) {
                $(".navbar").addClass("bg-green");
            } else {
                $(".navbar").removeClass("bg-green");
            }
        });

        $.AjaxRequest('#frmKritikSaran', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.reload();
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        $(document).ready(function() {
            let height = $('.so-detail').height();

            $('.so-slider .so-item').css('height', height * 1.4 + 'px');
        })

        function pesan(nomorhp) {
            $('#hubungi_penjual').modal('show');
            $('#whatsapp_penjual, #telepon_penjual').html(nomorhp);
            $('#card_hubungi_whatsapp').attr('onclick', `window.location.href = 'https://api.whatsapp.com/send?phone=${nomorhp}&text=Saya%20Tertarik%20dengan%20jualan%20Anda'`)
            $('#card_hubungi_telepon').attr('onclick', `window.open('tel:${nomorhp}')`);
        }

        window.addEventListener('load', (event) => {
            setTraffic();

            var email = document.getElementById('email_protection');
            var email_address = atob(email.innerHTML);

            email.innerHTML = email_address;

            <?php if ($coords != null) : ?>
                var map = new google.maps.Map(document.getElementById("map"), {
                    zoom: 13,
                    center: <?= $firstcoords ?>
                });

                var infowindow = new google.maps.InfoWindow();
                var boundaryCoords = <?= $coords ?>;

                var boundary = new google.maps.Polygon({
                    paths: boundaryCoords,
                    strokeColor: "#FF0000", // Warna stroke
                    strokeOpacity: 0.8, // Opasitas stroke
                    strokeWeight: 2 // Ketebalan stroke
                });

                boundary.setMap(map);
            <?php endif; ?>
        });

        function setTraffic() {
            $.ajax({
                url: "<?= base_url() . '/traffic/add' ?>",
                method: 'POST',
                dataType: 'json',
                success: function(response) {
                    console.log(response.MESSAGE);
                }
            });
        }

        jQuery(document).ready(function() {
            jQuery('[data-vbg]').youtube_background();
        });
    </script>
</body>

</html>