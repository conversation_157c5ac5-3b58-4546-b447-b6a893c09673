<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?= $title ?> - <?= isset($setting->desa) ? 'Desa ' . $setting->desa : 'Go Digital Desa' ?></title>
    <meta name="description" content="Detail Program Bantuan Sosial: <?= $penerimaan_blt->deskripsi ?> - <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta name="keywords" content="bantuan sosial, BLT, bantuan langsung tunai, program bantuan, <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta name="robots" content="index, follow">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <!-- Open Graph / Facebook -->
    <meta property="og:site_name" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= base_url('bantuan-sosial/detail/' . $penerimaan_blt->id) ?>">
    <meta property="og:title" content="<?= $title ?> - <?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:description" content="Detail Program Bantuan Sosial: <?= $penerimaan_blt->deskripsi ?> - <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta property="og:image" content="">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?= base_url('bantuan-sosial/detail/' . $penerimaan_blt->id) ?>">
    <meta property="twitter:title" content="<?= $title ?> - <?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="twitter:description" content="Detail Program Bantuan Sosial: <?= $penerimaan_blt->deskripsi ?> - <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta property="twitter:image" content="">

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="canonical" href="<?= base_url('bantuan-sosial/detail/' . $penerimaan_blt->id) ?>">

    <style>
        .navbar .navbar-nav .nav-item {
            padding-left: 5px;
            padding-right: 5px;
        }

        .bg-green {
            background-color: #007755 !important;
        }

        .hero-section {
            background: linear-gradient(135deg, #007755 0%, #005544 100%);
            color: white;
            padding: 120px 0 80px;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .content-section {
            padding: 80px 0;
        }

        .stat-item h3 {
            font-size: 2.5rem;
            font-weight: 600;
        }

        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }

        .badge {
            font-size: 0.8rem;
        }

        .breadcrumb {
            background: transparent;
            padding: 0;
        }

        .breadcrumb-item+.breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.5);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 1.8rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .stat-item h3 {
                font-size: 2rem;
            }

            .table-responsive {
                font-size: 0.9rem;
            }
        }

        /* Footer Styles */
        footer {
            background-color: #007755;
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }

        footer .header {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #E8EF4D;
        }

        footer .detail {
            font-size: 14px;
            margin-bottom: 8px;
            color: white;
        }

        footer .sitelink ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        footer .sitelink li {
            margin-bottom: 8px;
        }

        footer .sitelink a {
            color: white;
            text-decoration: none;
            font-size: 14px;
        }

        footer .sitelink a:hover {
            color: #E8EF4D;
        }

        footer .copyright {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <div class="d-flex flex-row">
                    <div>
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img src="<?= asset_url($setting->logo_desa) ?>" alt="" width="50">
                        <?php endif; ?>
                    </div>
                    <div class="d-flex flex-column ml-3">
                        <span class="m-0"><?= isset($setting->desa) ? $setting->desa : null ?></span>
                        <small class="m-0"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></small>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('pemerintah') ?>">Pemerintahan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('infografi') ?>">Infografis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('belanja') ?>">Belanja</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('berita/all') ?>">Berita</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('surat') ?>">Surat</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-toggle="dropdown" aria-expanded="false">
                            Lainnya
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item active" href="<?= base_url('bantuan-sosial') ?>">
                                <i class="fas fa-hands-helping me-2"></i>Bantuan Sosial
                            </a>
                            <a class="dropdown-item" href="<?= base_url('auth/login') ?>">Login CMS</a>
                            <a class="dropdown-item" href="<?= base_url('pengaduan') ?>">Pengaduan Masyarakat</a>
                            <a class="dropdown-item" href="<?= base_url('guestbook') ?>">Buku Tamu Digital</a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-3">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url() ?>" class="text-white-50">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('bantuan-sosial') ?>" class="text-white-50">Bantuan Sosial</a>
                            </li>
                            <li class="breadcrumb-item active text-white" aria-current="page">Detail</li>
                        </ol>
                    </nav>
                    <h1 class="hero-title"><?= $penerimaan_blt->deskripsi ?></h1>
                    <p class="hero-subtitle">
                        <i class="fas fa-money-bill-wave"></i>
                        Nominal: Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?>
                    </p>
                    <p class="hero-subtitle">
                        <i class="fas fa-calendar"></i>
                        Dibuat: <?= date('d F Y', strtotime($penerimaan_blt->tanggal_dibuat)) ?>
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="text-center">
                        <i class="fas fa-users fa-8x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="content-section">
        <div class="container">
            <!-- Summary Stats -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h3 class="text-primary mb-2"><?= count($penerima) ?></h3>
                                        <p class="text-muted mb-0">Total Penerima Terverifikasi</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h3 class="text-success mb-2">Rp <?= number_format($penerimaan_blt->nominal * count($penerima), 0, ',', '.') ?></h3>
                                        <p class="text-muted mb-0">Total Dana Tersalurkan</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h3 class="text-info mb-2">Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?></h3>
                                        <p class="text-muted mb-0">Nominal per Penerima</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daftar Penerima -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Daftar Penerima Bantuan Sosial
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($penerima)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="thead-light">
                                            <tr>
                                                <th width="5%">No</th>
                                                <th width="20%">NIK</th>
                                                <th width="25%">Nama</th>
                                                <th width="35%">Alamat</th>
                                                <th width="15%">RT/RW</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($penerima as $key => $value): ?>
                                                <tr>
                                                    <td><?= $key + 1 ?></td>
                                                    <td>
                                                        <span class="badge badge-secondary"><?= $value->nik ?></span>
                                                    </td>
                                                    <td>
                                                        <strong><?= $value->nama ?></strong>
                                                    </td>
                                                    <td><?= $value->alamat ?></td>
                                                    <td>
                                                        <span class="badge badge-info">RT <?= $value->rt ?> / RW <?= $value->rw ?></span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-users-slash fa-5x text-muted mb-3"></i>
                                    <h4 class="text-muted">Belum Ada Penerima Terverifikasi</h4>
                                    <p class="text-muted">Saat ini belum ada penerima yang terverifikasi untuk program bantuan sosial ini.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h6 class="mb-2">
                                        <i class="fas fa-info-circle text-primary"></i>
                                        Catatan Penting
                                    </h6>
                                    <p class="mb-0 text-muted">
                                        Data yang ditampilkan adalah penerima bantuan sosial yang telah melalui proses verifikasi dan dinyatakan layak menerima bantuan.
                                        Jika ada pertanyaan atau keberatan, silakan hubungi kantor desa.
                                    </p>
                                </div>
                                <div class="col-lg-4 text-lg-right">
                                    <a href="<?= base_url('bantuan-sosial') ?>" class="btn btn-outline-primary mr-2">
                                        <i class="fas fa-arrow-left"></i>
                                        Kembali
                                    </a>
                                    <a href="<?= base_url('guestbook') ?>" class="btn btn-primary">
                                        <i class="fas fa-phone"></i>
                                        Hubungi Kami
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex flex-row item">
                        <div>
                            <?php if (isset($setting->logo_desa)) : ?>
                                <img class="img-logo" width="50" src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" />
                            <?php endif; ?>
                        </div>

                        <div class="ml-3">
                            <p class="nama-desa mb-0" style="font-size: 20px; color: #E8EF4D; font-weight: 700;"><?= isset($setting->desa) ? $setting->desa : null ?></p>
                            <p class="kab-desa mb-0" style="font-size: 16px;"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="item">
                        <p class="header">Alamat</p>
                        <p class="detail alamat"><?= isset($setting->alamat) ? $setting->alamat : null ?></p>

                        <p class="header">Kontak Penting</p>
                        <?php foreach ($kontakpenting as $key => $value) : ?>
                            <p class="detail"><?= $value->nama ?>: <a href="tel:<?= $value->no_telp ?>" class="text-decoration-none text-white"><?= $value->no_telp ?></a></p>
                        <?php endforeach; ?>

                        <p class="detail mt-1" id="email_protection"><?= isset($setting->email) ? base64_encode($setting->email) : null ?></p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="item">
                        <p class="header">Sitemap</p>

                        <div class="sitelink">
                            <ul>
                                <li>
                                    <a href="<?= base_url('pemerintah') ?>" class="text-decoration-none text-white">
                                        Pemerintahan
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('infografi') ?>" class="text-decoration-none text-white">
                                        Infografis
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('belanja') ?>" class="text-decoration-none text-white">
                                        Belanja
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('berita/all') ?>" class="text-decoration-none text-white">
                                        Berita
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('surat') ?>" class="text-decoration-none text-white">
                                        Surat
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('bantuan-sosial') ?>" class="text-decoration-none text-white">
                                        Bantuan Sosial
                                    </a>
                                </li>
                            </ul>

                            <ul>
                                <li>
                                    <a href="<?= isset($setting->facebook) ? validate_sosmedurl($setting->facebook, 'facebook') : 'javascript:;' ?>" target="_blank">Facebook</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->twitter) ? validate_sosmedurl($setting->twitter, 'twitter') : 'javascript:;' ?>" target="_blank">Twitter</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->youtube) ? validate_sosmedurl($setting->youtube, 'youtube') : 'javascript:;' ?>" target="_blank">Youtube</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->instagram) ? validate_sosmedurl($setting->instagram, 'instagram') : 'javascript:;' ?>" target="_blank">Instagram</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 text-center">
                    <p class="copyright">&copy; <?= date('Y') ?> Go Digital Desa (<?= strtoupper(getPlatformName()) ?>). All Rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="<?= asset_url() ?>assets/templatesv2/js/jquery-3.6.0.min.js"></script>
    <script src="<?= asset_url() ?>assets/templatesv2/js/bootstrap.min.js"></script>

    <script>
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();

            if (scroll >= 200) {
                $(".navbar").addClass("bg-green");
            } else {
                $(".navbar").removeClass("bg-green");
            }
        });

        // Decode email protection
        $(document).ready(function() {
            var email = document.getElementById('email_protection');
            if (email && email.innerHTML) {
                var email_address = atob(email.innerHTML);
                email.innerHTML = email_address;
            }
        });
    </script>
</body>

</html>