<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?= $title ?> - <?= isset($setting->desa) ? 'Desa ' . $setting->desa : 'Go Digital Desa' ?></title>
    <meta name="description" content="Informasi Program Bantuan Sosial untuk Masyarakat <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta name="keywords" content="bantuan sosial, BLT, bantuan langsung tunai, program bantuan, <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta name="robots" content="index, follow">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <!-- Open Graph / Facebook -->
    <meta property="og:site_name" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= base_url('bantuan-sosial') ?>">
    <meta property="og:title" content="<?= $title ?> - <?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:description" content="Informasi Program Bantuan Sosial untuk Masyarakat <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta property="og:image" content="">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?= base_url('bantuan-sosial') ?>">
    <meta property="twitter:title" content="<?= $title ?> - <?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="twitter:description" content="Informasi Program Bantuan Sosial untuk Masyarakat <?= isset($setting->desa) ? $setting->desa : '' ?>">
    <meta property="twitter:image" content="">

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templatesv2/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="canonical" href="<?= base_url('bantuan-sosial') ?>">

    <style>
        .navbar .navbar-nav .nav-item {
            padding-left: 5px;
            padding-right: 5px;
        }

        .bg-green {
            background-color: #007755 !important;
        }

        .hero-section {
            background: linear-gradient(135deg, #007755 0%, #005544 100%);
            color: white;
            padding: 120px 0 80px;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content-section {
            padding: 80px 0;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .card {
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        .stat-item h6 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .badge {
            font-size: 0.75rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .section-header h2 {
                font-size: 2rem;
            }
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <div class="d-flex flex-row">
                    <div>
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img src="<?= asset_url($setting->logo_desa) ?>" alt="" width="50">
                        <?php endif; ?>
                    </div>
                    <div class="d-flex flex-column ml-3">
                        <span class="m-0"><?= isset($setting->desa) ? $setting->desa : null ?></span>
                        <small class="m-0"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></small>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('pemerintah') ?>">Pemerintahan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('infografi') ?>">Infografis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('belanja') ?>">Belanja</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('berita/all') ?>">Berita</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('surat') ?>">Surat</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-toggle="dropdown" aria-expanded="false">
                            Lainnya
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item active" href="<?= base_url('bantuan-sosial') ?>">
                                <i class="fas fa-hands-helping me-2"></i>Bantuan Sosial
                            </a>
                            <a class="dropdown-item" href="<?= base_url('auth/login') ?>">Login CMS</a>
                            <a class="dropdown-item" href="<?= base_url('pengaduan') ?>">Pengaduan Masyarakat</a>
                            <a class="dropdown-item" href="<?= base_url('guestbook') ?>">Buku Tamu Digital</a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="hero-title">Bantuan Sosial</h1>
                    <p class="hero-subtitle">Informasi Program Bantuan Sosial untuk Masyarakat <?= isset($setting->desa) ? $setting->desa : '' ?></p>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-hands-helping fa-10x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-header text-center mb-5">
                        <h2>Program Bantuan Sosial</h2>
                        <p class="text-muted">Daftar program bantuan sosial yang telah disalurkan kepada masyarakat</p>
                    </div>
                </div>
            </div>

            <?php if (!empty($penerimaan_blt)): ?>
                <div class="row">
                    <?php foreach ($penerimaan_blt as $key => $value): ?>
                        <div class="col-lg-6 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h5 class="card-title text-primary"><?= $value->deskripsi ?></h5>
                                        <span class="badge badge-success">Aktif</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h6 class="text-success mb-2">
                                            <i class="fas fa-money-bill-wave"></i>
                                            Rp <?= number_format($value->nominal, 0, ',', '.') ?>
                                        </h6>
                                    </div>

                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h6 class="text-primary mb-1"><?= $value->total_penerima ?></h6>
                                                <small class="text-muted">Total Penerima</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h6 class="text-success mb-1"><?= $value->terverifikasi ?></h6>
                                                <small class="text-muted">Terverifikasi</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h6 class="text-warning mb-1"><?= $value->menunggu_verifikasi ?></h6>
                                                <small class="text-muted">Menunggu</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i>
                                            <?= date('d M Y', strtotime($value->tanggal_dibuat)) ?>
                                        </small>
                                        <a href="<?= base_url('bantuan-sosial/detail/' . $value->id) ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                            Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-info-circle fa-5x text-muted mb-3"></i>
                            <h4 class="text-muted">Belum Ada Program Bantuan Sosial</h4>
                            <p class="text-muted">Saat ini belum ada program bantuan sosial yang tersedia.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Info Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h5 class="mb-2">
                                        <i class="fas fa-info-circle text-primary"></i>
                                        Informasi Penting
                                    </h5>
                                    <p class="mb-0 text-muted">
                                        Data yang ditampilkan adalah penerima bantuan sosial yang telah terverifikasi. 
                                        Untuk informasi lebih lanjut, silakan hubungi kantor desa.
                                    </p>
                                </div>
                                <div class="col-lg-4 text-lg-right">
                                    <a href="<?= base_url('guestbook') ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-phone"></i>
                                        Hubungi Kami
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="<?= asset_url() ?>assets/templatesv2/js/jquery-3.6.0.min.js"></script>
    <script src="<?= asset_url() ?>assets/templatesv2/js/bootstrap.min.js"></script>

    <script>
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();

            if (scroll >= 200) {
                $(".navbar").addClass("bg-green");
            } else {
                $(".navbar").removeClass("bg-green");
            }
        });
    </script>
</body>

</html>
