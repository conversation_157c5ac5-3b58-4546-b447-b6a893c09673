<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<!DOCTYPE html>
<html dir="ltr" lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Tell the browser to be responsive to screen width -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <!-- Favicon icon -->
    <link href="https://gides.id/assets/img/favicon.png" rel="icon">
    <title><?= isset($title) ? $title : 'Default Title' ?></title>

    <!-- Custom CSS -->
    <link href="<?= asset_url() ?>assets/extra-libs/c3/c3.min.css" rel="stylesheet">
    <link href="<?= asset_url() ?>assets/libs/chartist/dist/chartist.min.css" rel="stylesheet">
    <link href="<?= asset_url() ?>assets/extra-libs/jvector/jquery-jvectormap-2.0.2.css" rel="stylesheet" />
    <link href="<?= asset_url() ?>assets/extra-libs/datatables.net-bs4/css/dataTables.bootstrap4.css" rel="stylesheet">
    <link href="<?= asset_url() ?>assets/extra-libs/bootstrap-multiselect/dist/css/bootstrap-multiselect.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="<?= asset_url() ?>assets/dist/css/jquery.uploader.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/extra-libs/bootstrap-tagsinput/dist/bootstrap-tagsinput.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" integrity="sha512-mSYUmp1HYZDFaVKK//63EcZq4iFWFjxSL+Z3T/aCt4IO9Cejm03q3NKKYN6pFQzY0SBOr8h+eCIAZHPXcpZaNw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        .fa-trash-o::before {
            content: "\f1f8"
        }
    </style>

    <!-- Custom CSS -->
    <link href="<?= asset_url() ?>assets/dist/css/style.min.css" rel="stylesheet">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <style>
        .sidebar-nav #sidebarnav .sidebar-item.selected>.sidebar-link {
            background: #18BAB1 !important;
        }

        .bootstrap-tagsinput {
            display: block;
            line-height: 1.5;
            height: calc(1.5em + .75rem + 2px);
            border: 1px solid #e9ecef;
            padding: .375rem .75rem;
            box-shadow: unset;
            border-radius: 2px;
        }

        .ai-popup .card {
            max-width: 400px;
            min-width: 400px;
            box-shadow: 1px 1px 3px #888888;
        }

        .ai-popup .card .card-body {
            max-height: 400px;
            overflow: auto;
        }

        .ai-popup .card .card-header .card-title {
            margin-bottom: 0;
        }

        .chat-popup {
            position: fixed;
            bottom: -100%;
            transition: all .25s ease-in-out;
            right: 2rem;
        }

        .chat-popup.show {
            bottom: 5rem;
        }

        .chat-logo {
            position: relative;
        }

        .chat-logo.hidden {
            left: -10rem;
        }

        .dot {
            display: inline-block;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: #4b9cdb;
        }

        @keyframes jumpingAnimation {
            0% {
                transform: translate3d(0, 0, 0);
            }

            50% {
                transform: translate3d(0, 3px, 0);
            }

            100% {
                transform: translate3d(0, 0, 0);
            }
        }

        .dot-container {
            display: flex;
            padding-top: 3px;
            padding-bottom: 7px;
        }

        .dot-container .dot:nth-last-child(1) {
            animation: jumpingAnimation 0.6s 0.1s ease-in infinite;
            margin-right: 5px;
        }

        .dot-container .dot:nth-last-child(2) {
            animation: jumpingAnimation 0.6s 0.2s ease-in infinite;
            margin-right: 5px;
        }

        .dot-container .dot:nth-last-child(3) {
            animation: jumpingAnimation 0.6s 0.3s ease-in infinite;
            margin-right: 5px;
        }

        .select2-container .select2-selection--multiple {
            min-height: 38px;
        }

        .select2-container--default .select2-selection--single,
        .select2-container--default .select2-selection--multiple {
            border: 1px solid #e9ecef;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 35px;
        }
    </style>
</head>

<body>
    <div class="loading-ajax" style="position: fixed;top: 0;left: 0;right: 0;bottom: 0;background: rgb(255 255 255 / 75%);z-index: 99999;display: flex;justify-content: center;align-items: center;flex-direction: column;opacity: 0;transition-duration: .25s;transition-timing-function: ease-in-out;visibility: hidden;">
        <div class="text-center d-flex flex-column justify-content-center">
            <?php if (getPlatformName() == 'Gides') : ?>
                <img src="<?= asset_url() ?>assets/images/gides-logo.png" alt="" width="25%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'GidesManis') : ?>
                <img src="<?= asset_url() ?>assets/images/gidesmanis-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'SmartDes'): ?>
                <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php elseif (getPlatformName() == 'ProGides'): ?>
                <img src="<?= asset_url() ?>assets/images/progides-logo.png" alt="" width="50%" class="m-auto pb-5">
            <?php endif; ?>

            <div class="spinner-border text-primary m-auto" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    </div>

    <!-- ============================================================== -->
    <!-- Preloader - style you can find in spinners.css -->
    <!-- ============================================================== -->
    <div class="preloader">
        <div class="lds-ripple">
            <div class="lds-pos"></div>
            <div class="lds-pos"></div>
        </div>
    </div>

    <!-- ============================================================== -->
    <!-- Main wrapper - style you can find in pages.scss -->
    <!-- ============================================================== -->
    <div id="main-wrapper" data-theme="light" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full" data-sidebar-position="fixed" data-header-position="fixed" data-boxed-layout="full">
        <!-- ============================================================== -->
        <!-- Topbar header - style you can find in pages.scss -->
        <!-- ============================================================== -->
        <header class="topbar" data-navbarbg="skin6">
            <nav class="navbar top-navbar navbar-expand-md">
                <div class="navbar-header" data-logobg="skin6">
                    <!-- This is for the sidebar toggle which is visible on mobile only -->
                    <a class="nav-toggler waves-effect waves-light d-block d-md-none" href="javascript:void(0)"><i class="ti-menu ti-close"></i></a>

                    <!-- ============================================================== -->
                    <!-- Logo -->
                    <!-- ============================================================== -->
                    <div class="navbar-brand">
                        <!-- Logo icon -->
                        <a href="<?= base_url() ?>">
                            <!-- Logo text -->
                            <span class="logo-text">
                                <?php if (getPlatformName() == 'Gides') : ?>
                                    <!-- dark Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/logo.png" alt="homepage" class="dark-logo mt-3" width="200" />

                                    <!-- Light Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/logo.png" class="light-logo mt-3" alt="homepage" width="200" />
                                <?php elseif (getPlatformName() == 'GidesManis') : ?>
                                    <!-- dark Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/gidesmanis-logo.png" alt="homepage" class="dark-logo mt-3" width="200" />

                                    <!-- Light Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/gidesmanis-logo.png" class="light-logo mt-3" alt="homepage" width="200" />
                                <?php elseif (getPlatformName() == 'SmartDes') : ?>
                                    <!-- dark Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="homepage" class="dark-logo mt-3" width="200" />

                                    <!-- Light Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" class="light-logo mt-3" alt="homepage" width="200" />
                                <?php elseif (getPlatformName() == 'ProGides') : ?>
                                    <!-- dark Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/progides-logo.png" alt="homepage" class="dark-logo mt-3" width="200" />

                                    <!-- Light Logo text -->
                                    <img src="<?= asset_url() ?>assets/images/progides-logo.png" class="light-logo mt-3" alt="homepage" width="200" />
                                <?php endif; ?>
                            </span>
                        </a>
                    </div>
                    <!-- ============================================================== -->
                    <!-- End Logo -->
                    <!-- ============================================================== -->

                    <!-- ============================================================== -->
                    <!-- Toggle which is visible on mobile only -->
                    <!-- ============================================================== -->
                    <a class="topbartoggler d-block d-md-none waves-effect waves-light" href="javascript:void(0)" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><i class="ti-more"></i></a>
                </div>
                <!-- ============================================================== -->
                <!-- End Logo -->
                <!-- ============================================================== -->

                <div class="navbar-collapse collapse" id="navbarSupportedContent">
                    <!-- ============================================================== -->
                    <!-- toggle and nav items -->
                    <!-- ============================================================== -->
                    <ul class="navbar-nav float-left mr-auto ml-3 pl-1">
                        <!-- Notification -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle pl-md-3 position-relative" href="javascript:void(0)"
                                id="bell" role="button" data-toggle="dropdown" aria-haspopup="true"
                                aria-expanded="false">
                                <span><i data-feather="bell" class="svg-icon"></i></span>
                                <span class="badge badge-primary notify-no rounded-circle"><?= count(getCurrentNotification()) ?></span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-left mailbox animated bounceInDown">
                                <ul class="list-style-none">
                                    <li>
                                        <div class="message-center notifications position-relative">
                                            <?php foreach (getCurrentNotification() as $key => $value): ?>
                                                <!-- Message -->
                                                <a href="<?= $value->link ?>?read=<?= $value->id ?>"
                                                    class="message-item d-flex align-items-center border-bottom px-3 py-2">
                                                    <div class="btn btn-danger rounded-circle btn-circle">
                                                        <i class="fa fa-bell text-white"></i>
                                                    </div>
                                                    <div class="w-75 d-inline-block v-middle pl-2">
                                                        <h6 class="message-title mb-0 mt-1">Sistem</h6>
                                                        <span class="font-12 text-nowrap d-block text-muted"><?= $value->message ?></span>
                                                        <span class="font-12 text-nowrap d-block text-muted"><?= tgl_indo($value->createddate) ?></span>
                                                    </div>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </li>

                                    <li>
                                        <a class="nav-link pt-3 text-center text-dark" href="<?= base_url('notification') ?>">
                                            <strong>Lihat semua notifikasi</strong>
                                            <i class="fa fa-angle-right"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <!-- End Notification -->
                    </ul>

                    <!-- ============================================================== -->
                    <!-- Right side toggle and nav items -->
                    <!-- ============================================================== -->
                    <ul class="navbar-nav float-right">
                        <!-- ============================================================== -->
                        <!-- User profile and search -->
                        <!-- ============================================================== -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <img src="<?= getCurrentProfileImage() ?>" alt="user" class="rounded-circle" width="40" height="40" style="object-fit: cover; object-position: center;">
                                <span class="ml-2 d-none d-lg-inline-block">
                                    <span>Hello,</span>
                                    <span class="text-dark"><?= getSessionValue('USERNAME') ?></span>
                                    <i data-feather="chevron-down" class="svg-icon"></i>
                                </span>
                            </a>

                            <div class="dropdown-menu dropdown-menu-right user-dd animated flipInY">
                                <?php if (!isOperatorDesa()) : ?>
                                    <a href="<?= base_url('changeprofile') ?>" class="dropdown-item">
                                        <i class="fa fa-key mr-2 ml-1"></i>
                                        Ubah Profil
                                    </a>
                                <?php endif; ?>

                                <a class="dropdown-item" href="<?= base_url('logout') ?>">
                                    <i data-feather="power" class="svg-icon mr-2 ml-1"></i>
                                    Logout
                                </a>
                            </div>
                        </li>
                        <!-- ============================================================== -->
                        <!-- User profile and search -->
                        <!-- ============================================================== -->
                    </ul>
                </div>
            </nav>
        </header>
        <!-- ============================================================== -->
        <!-- End Topbar header -->
        <!-- ============================================================== -->

        <!-- ============================================================== -->
        <!-- Left Sidebar - style you can find in sidebar.scss  -->
        <!-- ============================================================== -->
        <aside class="left-sidebar" data-sidebarbg="skin6">
            <!-- Sidebar scroll-->
            <div class="scroll-sidebar" data-sidebarbg="skin6">
                <!-- Sidebar navigation-->
                <nav class="sidebar-nav">
                    <ul id="sidebarnav">
                        <?php if (!isOperatorDesa()): ?>
                            <li class="sidebar-item">
                                <a class="sidebar-link" href="<?= base_url('admin') ?>" aria-expanded="false">
                                    <i data-feather="home" class="feather-icon"></i>
                                    <span class="hide-menu">Dashboard</span>
                                </a>
                            </li>

                            <?php if (isPMD()) : ?>
                                <li class="sidebar-item">
                                    <a class="sidebar-link" href="<?= base_url('pmd/leader') ?>" aria-expanded="false">
                                        <i class="fa fa-users"></i>
                                        <span class="hide-menu">Pimpinan PMD</span>
                                    </a>
                                </li>

                                <li class="sidebar-item">
                                    <a class="sidebar-link" href="<?= base_url('pmd/member') ?>" aria-expanded="false">
                                        <i class="fa fa-users"></i>
                                        <span class="hide-menu">Anggota PMD</span>
                                    </a>
                                </li>

                                <li class="sidebar-item">
                                    <a class="sidebar-link" href="<?= base_url('pmd/disposisi') ?>" aria-expanded="false">
                                        <i class="fa fa-recycle"></i>
                                        <span class="hide-menu">Disposisi</span>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php if (getSessionValue('ROLE') == 'admin' || isKecamatan_withWeb()) : ?>
                                <?php if (isAdmin()) : ?>
                                    <li class="sidebar-item">
                                        <a class="sidebar-link has-arrow" href="javascript:;" aria-expanded="false">
                                            <i class="fa fa-cogs"></i>
                                            <span class="hide-menu">Master</span>
                                        </a>

                                        <ul aria-expanded="false" class="collapse first-level base-level-line">
                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('master/bidangpengaduan') ?>">
                                                    <span class="hide-menu">Bidang Pengaduan</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('master/bidangperangkat') ?>">
                                                    <span class="hide-menu">Bidang Perangkat Desa</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('master/kategoridokumen') ?>" class="sidebar-link">
                                                    <span class="hide-menu">Kategori Dokumen</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('warga') ?>" aria-expanded="false">
                                                    <span class="hide-menu">Data Warga</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('penandatangan') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Penandatangan</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('asset') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Aset</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('operator_desa') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Operator Desa</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('autoreplywa') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Auto Reply WA</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                <li class="sidebar-item">
                                    <a class="sidebar-link has-arrow" href="javascript:void(0)" aria-expanded="false">
                                        <i class="fa fa-newspaper"></i>
                                        <span class="hide-menu">Konten</span>
                                    </a>

                                    <ul aria-expanded="false" class="collapse  first-level base-level-line">
                                        <li class="sidebar-item">
                                            <a class="sidebar-link" href="<?= base_url('berita') ?>" aria-expanded="false">
                                                <span class="hide-menu">Berita</span>
                                            </a>
                                        </li>

                                        <li class="sidebar-item">
                                            <a class="sidebar-link" href="<?= base_url('produk') ?>" aria-expanded="false">
                                                <span class="hide-menu">Produk</span>
                                            </a>
                                        </li>

                                        <?php if (isAdmin()) : ?>
                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('produkhukum') ?>" aria-expanded="false">
                                                    <span class="hide-menu">Produk Hukum</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('wisata') ?>" aria-expanded="false">
                                                    <span class="hide-menu">Wisata</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('slider') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Slider</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('floworganizationchart') ?>" class="sidebar-link">
                                                    <span class="hide-menu">Alur Jabatan Struktur Organisasi</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('strukturorganisasi') ?>" class="sidebar-link">
                                                    <span class="hide-menu">Struktur Organisasi</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </li>

                                <?php if (isAdmin() || isKecamatan_withWeb()) : ?>
                                    <?php if (isAdmin()) : ?>
                                        <li class="sidebar-item">
                                            <a class="sidebar-link has-arrow" href="javascript:void(0)" aria-expanded="false">
                                                <i class="fa fa-users"></i>
                                                <span class="hide-menu">Program</span>
                                            </a>

                                            <ul aria-expanded="false" class="collapse  first-level base-level-line">
                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url(isKelurahan() ? 'anggarankelurahan' : 'apbdesa') ?>" aria-expanded="false">
                                                        <span class="hide-menu"><?= isKelurahan() ? 'Anggaran Kelurahan' : 'APB Desa' ?></span>
                                                    </a>
                                                </li>

                                                <li class="sidebar-item">
                                                    <a href="<?= base_url('timelinetask') ?>" class="sidebar-link">
                                                        <span class="hide-menu">Timeline Tugas</span>
                                                    </a>
                                                </li>

                                                <li class="sidebar-item">
                                                    <a href="<?= base_url('penerimaan_blt') ?>" class="sidebar-link">
                                                        <span class="hide-menu">Penerimaan BLT</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                    <?php endif; ?>

                                    <li class="sidebar-item">
                                        <a class="sidebar-link has-arrow" href="javascript:void(0)" aria-expanded="false">
                                            <i data-feather="bar-chart" class="feather-icon"></i>
                                            <span class="hide-menu">Infografis</span>
                                        </a>

                                        <ul aria-expanded="false" class="collapse  first-level base-level-line">
                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('kategori_infografis') ?>" aria-expanded="false">
                                                    <span class="hide-menu">Kategori</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a class="sidebar-link" href="<?= base_url('infografis') ?>" aria-expanded="false">
                                                    <span class="hide-menu">Data Penduduk</span>
                                                </a>
                                            </li>

                                            <?php if (isAdmin()) : ?>
                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url('migrasi') ?>" aria-expanded="false">
                                                        <span class="hide-menu">Migrasi Antar-Desa</span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>

                                    <?php if (isAdmin() || isKecamatan_withWeb()) : ?>
                                        <?php if (isAdmin()) : ?>
                                            <li class="sidebar-item">
                                                <a class="sidebar-link has-arrow" href="javascript:void(0)" aria-expanded="false">
                                                    <i class="fa fa-cogs"></i>
                                                    <span class="hide-menu">Setting</span>
                                                </a>

                                                <ul aria-expanded="false" class="collapse  first-level base-level-line">
                                                    <li class="sidebar-item">
                                                        <a class="sidebar-link" href="<?= base_url('settingumum') ?>" aria-expanded="false">
                                                            <span class="hide-menu">Setting Umum</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a class="sidebar-link" href="<?= base_url('settingmap') ?>" aria-expanded="false">
                                                            <span class="hide-menu">Batas Wilayah</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a class="sidebar-link" href="<?= base_url('kontakpenting') ?>" aria-expanded="false">
                                                            <span class="hide-menu">Kontak Penting</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('kategori_surat') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Kategori Surat</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('whatsappgateway') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">WhatsApp Gateway</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('theme') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Tema</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('siades') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Sistem Absensi Desa</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('surat') ?>" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-envelope"></i>
                                                    <span class="hide-menu">Surat</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <li class="sidebar-item">
                                            <a href="<?= base_url('arsip/guestbook') ?>" class="sidebar-link" aria-expanded="false">
                                                <i class="fa fa-archive"></i>
                                                <span class="hide-menu">Arsip Buku Tamu</span>
                                            </a>
                                        </li>

                                        <?php if (isAdmin()) : ?>
                                            <li class="sidebar-item">
                                                <a href="<?= base_url('master/user_guide') ?>" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-book"></i>
                                                    <span class="hide-menu">Buku Panduan</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('arsip/pengaduan') ?>" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-archive"></i>
                                                    <span class="hide-menu">Arsip Pengaduan</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('surat/arsip/masuk') ?>" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-archive"></i>
                                                    <span class="hide-menu">Arsip Surat Masuk</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('surat/arsip/keluar') ?>" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-archive"></i>
                                                    <span class="hide-menu">Arsip Surat Keluar</span>
                                                </a>
                                            </li>

                                            <li class="sidebar-item">
                                                <a href="https://asset.gides.id/desktop/download/gides_installer.msi" class="sidebar-link" aria-expanded="false">
                                                    <i class="fa fa-download"></i>
                                                    <?php if (getPlatformName() != 'SmartDes' && getPlatformName() != 'ProGides'): ?>
                                                        <span class="hide-menu">Gides Installer</span>
                                                    <?php else: ?>
                                                        <?php if (getPlatformName() == 'SmartDes'): ?>
                                                            <span class="hide-menu">SmartDes Installer</span>
                                                        <?php else: ?>
                                                            <span class="hide-menu">ProGides Installer</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php elseif (!isPMD() && !isKecamatan()) : ?>
                                <li class="sidebar-item">
                                    <a class="sidebar-link has-arrow" href="javascript:void(0)" aria-expanded="false">
                                        <i class="fa fa-cogs"></i>
                                        <span class="hide-menu">Master</span>
                                    </a>

                                    <ul aria-expanded="false" class="collapse  first-level base-level-line">
                                        <?php if (isSuperAdmin() && isAllPlatform()): ?>
                                            <li class="sidebar-item">
                                                <a href="<?= base_url('manage/superadmin') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Super Admin</span>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <li class="sidebar-item">
                                            <a class="sidebar-link" href="<?= base_url('manage/admin') ?>" aria-expanded="false">
                                                <span class="hide-menu">Admin</span>
                                            </a>
                                        </li>

                                        <?php if (!isDiskominfo()) : ?>
                                            <?php if (isAllPlatform()): ?>
                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url('manage/diskominfo') ?>" aria-expanded="false">
                                                        <span class="hide-menu">Diskominfo</span>
                                                    </a>
                                                </li>

                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url('manage/pmd') ?>" aria-expanded="false">
                                                        <span class="hide-menu">PMD</span>
                                                    </a>
                                                </li>

                                                <li class="sidebar-item">
                                                    <a href="<?= base_url('manage/regency') ?>" class="sidebar-link" aria-expanded="false">
                                                        <span class="hide-menu">Kabupaten</span>
                                                    </a>
                                                </li>

                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url('manage/subdistrict') ?>" aria-expanded="false">
                                                        <span class="hide-menu">Kecamatan</span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <li class="sidebar-item">
                                                <a href="<?= base_url('manage/category/letters') ?>" class="sidebar-link" aria-expanded="false">
                                                    <span class="hide-menu">Kategori Surat</span>
                                                </a>
                                            </li>

                                            <?php if (isAllPlatform() || isSuperAdmin()): ?>
                                                <?php if (isAllPlatform()): ?>
                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('manage/category/budgets') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Kategori Anggaran</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('manage/category/news') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Kategori Berita</span>
                                                        </a>
                                                    </li>

                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('manage/news/notification') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Notifikasi Berita</span>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>

                                                <li class="sidebar-item">
                                                    <a href="<?= base_url('manage/news') ?>" class="sidebar-link" aria-expanded="false">
                                                        <span class="hide-menu">Berita</span>
                                                    </a>
                                                </li>

                                                <?php if (isAllPlatform()): ?>
                                                    <li class="sidebar-item">
                                                        <a href="<?= base_url('manage/slider_popup') ?>" class="sidebar-link" aria-expanded="false">
                                                            <span class="hide-menu">Slider Popup</span>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </ul>
                                </li>

                                <?php if (isSuperAdmin()) : ?>
                                    <?php if (isAllPlatform()): ?>
                                        <li class="sidebar-item">
                                            <a href="<?= base_url('master/user_guide') ?>" class="sidebar-link">
                                                <i class="fa fa-book"></i>
                                                <span class="hide-menu">Buku Panduan</span>
                                            </a>
                                        </li>

                                        <li class="sidebar-item">
                                            <a href="<?= base_url('adsense/reports') ?>" class="sidebar-link" aria-expanded="false">
                                                <i class="fa fa-dollar-sign"></i>
                                                <span class="hide-menu">Laporan Adsense</span>
                                            </a>
                                        </li>

                                        <li class="sidebar-item">
                                            <a class="sidebar-link" href="<?= base_url('manage/whatsapp') ?>" aria-expanded="false">
                                                <i class="fab fa-whatsapp"></i>
                                                <span class="hide-menu">Setting WhatsApp</span>
                                            </a>
                                        </li>

                                        <li class="sidebar-item">
                                            <a class="sidebar-link" href="<?= base_url('manage/prompt') ?>" aria-expanded="false">
                                                <i class="fa fa-bell"></i>
                                                <span class="hide-menu">Setting Prompt</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (isKecamatan() || isPMD()) : ?>
                                <?php if (isKecamatan()) : ?>
                                    <li class="sidebar-item">
                                        <a class="sidebar-link" href="<?= base_url('strukturorganisasi/permohonan') ?>" aria-expanded="false">
                                            <i class="fa fa-users"></i>
                                            <span class="hide-menu">Permohonan Struktur Organisasi</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <li class="sidebar-item">
                                    <a href="<?= base_url('timelinetask/request') ?>" class="sidebar-link">
                                        <i class="fa fa-users"></i>
                                        <span class="hide-menu">Permohonan Timeline Tugas</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php else: ?>
                            <li class="sidebar-item">
                                <a href="<?= base_url('timelinetask') ?>" class="sidebar-link">
                                    <i class="fa fa-list"></i>
                                    <span class="hide-menu">Timeline Tugas</span>
                                </a>
                            </li>

                            <?php foreach (getMenu() as $key => $value): ?>
                                <?php if ($value->has_submenu != 1): ?>
                                    <li class="sidebar-item">
                                        <a href="<?= base_url($value->url) ?>" class="sidebar-link">
                                            <i class="<?= $value->icon ?>"></i>
                                            <span class="hide-menu"><?= $value->name ?></span>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="sidebar-item">
                                        <a class="sidebar-link has-arrow" href="javascript:;" aria-expanded="false">
                                            <i class="<?= $value->icon ?>"></i>
                                            <span class="hide-menu"><?= $value->name ?></span>
                                        </a>

                                        <ul aria-expanded="false" class="collapse first-level base-level-line">
                                            <?php foreach (getSubMenu($value->id) as $k => $v): ?>
                                                <li class="sidebar-item">
                                                    <a class="sidebar-link" href="<?= base_url($v->url) ?>" aria-expanded="false">
                                                        <span class="hide-menu"><?= $v->name ?></span>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </nav>
                <!-- End Sidebar navigation -->
            </div>
            <!-- End Sidebar scroll-->
        </aside>
        <!-- ============================================================== -->
        <!-- End Left Sidebar - style you can find in sidebar.scss  -->
        <!-- ============================================================== -->

        <!-- ============================================================== -->
        <!-- Page wrapper  -->
        <!-- ============================================================== -->
        <div class="page-wrapper">
            <?php if (isset($content)) : ?>
                <?php $this->load->view($content); ?>
            <?php endif; ?>

            <!-- ============================================================== -->
            <!-- footer -->
            <!-- ============================================================== -->
            <footer class="footer text-center text-muted">
                All Rights Reserved by Adminmart. Designed and Developed by <a href="https://www.senusatech.com/">Senusatech</a>.
            </footer>
            <!-- ============================================================== -->
            <!-- End footer -->
            <!-- ============================================================== -->
        </div>
        <!-- ============================================================== -->
        <!-- End Page wrapper  -->
        <!-- ============================================================== -->
    </div>
    <!-- ============================================================== -->
    <!-- End Wrapper -->
    <!-- ============================================================== -->

    <div class="modal fade" id="modalGlobal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    </div>

    <div style="position: fixed; z-index: 1; right: 1.5rem; bottom: 1.5rem; cursor: pointer;" class="ai-popup">
        <div class="chat-popup">
            <div class="card">
                <div class="card-header">
                    <div>
                        <?php if (getPlatformName() != 'SmartDes' && getPlatformName() != 'ProGides'): ?>
                            <h4 class="card-title">Gides.ai - Web</h4>
                        <?php else: ?>
                            <?php if (getPlatformName() == 'SmartDes'): ?>
                                <h4 class="card-title">SmartDes.ai - Web</h4>
                            <?php else: ?>
                                <h4 class="card-title">ProGides.ai - Web</h4>
                            <?php endif; ?>
                        <?php endif; ?>
                        <small class="card-subtitle">Biasanya akan membalas dalam beberapa menit</small>
                    </div>
                </div>

                <div class="card-body">
                    <div id="firstchat">
                        <form id="frmStart" action="<?= base_url('gidesai/start') ?>" method="POST" autocomplete="off">
                            <div class="form-group">
                                <label for="">Pertanyaan</label>
                                <textarea name="message" class="form-control" placeholder="Masukkan Pertanyaan yang ingin kamu sampaikan" rows="5" style="resize: none;" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">Kirim Pertanyaan</button>
                        </form>
                    </div>

                    <div class="chat-room" id="chatroom" style="display: none;">
                    </div>

                    <div id="chat-type" style="display: none;">
                    </div>
                </div>

                <div class="card-footer" id="chatfooter" style="display: none;">
                    <form id="frmReply" action="<?= base_url('gidesai/start') ?>" method="POST" autocomplete="off">
                        <div class="input-group">
                            <input type="text" name="message" class="form-control" placeholder="Masukkan pertanyaan kamu disini" required>

                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div style="display: flex; justify-content: end;">
            <div style="background-color: #fff; padding: .75rem; border-radius: 50%; box-shadow: 1px 1px 3px #888888; cursor: pointer; overflow: hidden; width: 60px; height: 60px; display: flex; justify-content: center; align-items: center;" onclick="popupAI()">
                <?php if (getPlatformName() != 'SmartDes'): ?>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" class="chat-logo" width="40">
                <?php else: ?>
                    <img src="<?= asset_url() ?>assets/images/smartdes-logo.png" alt="" class="chat-logo" width="40">
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- ============================================================== -->
    <!-- All Jquery -->
    <!-- ============================================================== -->
    <script src="<?= asset_url() ?>assets/libs/jquery/dist/jquery.min.js"></script>
    <script src="<?= asset_url() ?>assets/libs/popper.js/dist/umd/popper.min.js"></script>
    <script src="<?= asset_url() ?>assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>

    <!-- apps -->
    <script src="<?= asset_url() ?>assets/dist/js/app-style-switcher.js"></script>
    <script src="<?= asset_url() ?>assets/dist/js/feather.min.js"></script>
    <script src="<?= asset_url() ?>assets/libs/perfect-scrollbar/dist/perfect-scrollbar.jquery.min.js"></script>
    <script src="<?= asset_url() ?>assets/dist/js/sidebarmenu.js"></script>

    <!-- Custom JavaScript -->
    <script src="<?= asset_url() ?>assets/dist/js/custom.min.js"></script>

    <!-- This page JavaScript -->
    <script src="<?= asset_url() ?>assets/extra-libs/c3/d3.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/c3/c3.min.js"></script>
    <script src="<?= asset_url() ?>assets/libs/chartist/dist/chartist.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/jvector/jquery-jvectormap-2.0.2.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/jvector/jquery-jvectormap-world-mill-en.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/bootstrap-multiselect/dist/js/bootstrap-multiselect.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js" integrity="sha512-T/tUfKSV1bihCnd+MxKD0Hm1uBBroVYBOYSk1knyvQ9VyZJpc/ALb4P0r6ubwVPSGB2GvjeoMAJJImBG12TiaQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script src="<?= asset_url() ?>assets/extra-libs/ckeditor/ckeditor.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/sweetalert/sweetalert.min.js"></script>

    <script src="<?= asset_url() ?>assets/js/jquery.uploader.min.js"></script>
    <script src="<?= asset_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= asset_url() ?>assets/js/script.js"></script>

    <script>
        $('.modal').on('shown.bs.modal', function() {
            $(document).off('focusin.modal');
        });

        $('.datatables').DataTable();

        $.AjaxRequest('#frm', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = $('#frm').attr('success-redirect');
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        function deleteData(nama, id) {
            swal({
                title: 'Apakah anda yakin?',
                text: `${nama} akan dihapus secara permanen dari database`,
                icon: 'warning',
                buttons: true,
                dangerMode: true
            }).then(isAccepted => {
                if (isAccepted) {
                    $.ajax({
                        url: '<?= base_url(uri_string() . '/delete') ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            id: id
                        },
                        success: function(response) {
                            if (response.RESULT == 'OK') {
                                return swalMessageSuccess(response.MESSAGE, ok => {
                                    return window.location.reload();
                                });
                            } else {
                                return swalMessageFailed(response.MESSAGE);
                            }
                        }
                    }).fail(function() {
                        return swalError();
                    })
                }
            })
        }

        function RemoveRogueChar(convertString) {
            if (convertString.substring(0, 1) == ",") {
                return convertString.substring(1, convertString.length)
            }

            return convertString;
        }

        function format(these) {
            if ($(these).val() != '') {
                let num = $(these).val();
                let decs = num.split('.');

                num = decs[0];
                num = num.replace(/,/gi, "").split("").reverse().join("");

                let num2 = RemoveRogueChar(num.replace(/(.{3})/g, "$1,").split("").reverse().join(""));
                if (decs.length > 1) {
                    num2 += '.' + decs[1];
                }

                $(these).val(num2);
            }
        }

        $(document).ajaxStart(function(e) {
            $('.loading-ajax').css('opacity', 1).css('visibility', 'visible');
        });

        $(document).ajaxComplete(function(e) {
            $('.loading-ajax').css('opacity', 0).css('visibility', 'hidden');
        });

        function popupAI() {
            if ($('.chat-popup').hasClass('show')) {
                $('.chat-popup').removeClass('show');
                $('img.chat-logo').attr('src', '<?= asset_url() ?>assets/images/gides.png').attr('width', '40px');
            } else {
                $('.chat-popup').addClass('show');
                $('img.chat-logo').attr('src', '<?= asset_url() ?>assets/images/close.png').attr('width', '20px');
            }
        }

        $('#frmStart').submit(function(e) {
            e.preventDefault();

            $('#firstchat').fadeOut();
            $('#chatroom').fadeIn();

            let message = $('#firstchat textarea[name=message]').val();

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start flex-row-reverse mb-4">
                <div>
                    <img src="<?= getCurrentProfileImage() ?>" alt="" width="25" height="25" style="border-radius: 50%; object-fit: cover; object-position: center;">
                </div>

                <div class="mr-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-right-radius: 0;">
                    <span>${message}</span>
                </div>
            </div>`);

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4 typing">
                <div>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                </div>

                <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                    <div class="dot-container">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>`);

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                dataType: 'json',
                data: {
                    message: message
                },
                success: function(response) {
                    $('#chatroom .typing').remove();

                    if (response.RESULT == 'OK') {
                        $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4">
                            <div>
                                <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                            </div>

                            <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                                <span>${response.DESCRIPTION}</span>
                            </div>
                        </div>`);

                        $('#chatfooter').fadeIn();
                    }
                }
            }).fail(function() {
                $('#chatroom .typing').remove();
            });
        });

        $('#frmReply').submit(function(e) {
            e.preventDefault();

            $('#chat-type').fadeOut();

            let message = $('#frmReply input[name=message]').val();

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start flex-row-reverse mb-4">
                <div>
                    <img src="<?= getCurrentProfileImage() ?>" alt="" width="25" height="25" style="border-radius: 50%; object-fit: cover; object-position: center;">
                </div>

                <div class="mr-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-right-radius: 0;">
                    <span>${message}</span>
                </div>
            </div>`);

            $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4 typing">
                <div>
                    <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                </div>

                <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                    <div class="dot-container">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>`);

            $('#frmReply input[name=message]').val(null);

            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                dataType: 'json',
                data: {
                    message: message
                },
                success: function(response) {
                    $('#chatroom .typing').remove();

                    if (response.RESULT == 'OK') {
                        $('#chatroom').append(`<div class="chat-item d-flex justify-content-start mb-4">
                            <div>
                                <img src="<?= asset_url() ?>assets/images/gides.png" alt="" width="25" style="border-radius: 50%;">
                            </div>

                            <div class="ml-2" style="background-color: #eee; padding: .5rem 1rem; border-radius: 10px; border-bottom-left-radius: 0;">
                                <span>${response.DESCRIPTION}</span>
                            </div>
                        </div>`);

                        $('#chatfooter').fadeIn();
                    }
                }
            }).fail(function() {
                $('#chatroom .typing').remove();
            });
        });
    </script>
</body>

</html>