<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary to-secondary py-20 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="4"/></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 relative z-10">
        <div class="flex flex-col lg:flex-row items-center">
            <div class="lg:w-2/3 text-center lg:text-left mb-8 lg:mb-0">
                <!-- Breadcrumb -->
                <nav class="mb-6">
                    <ol class="flex flex-wrap text-white text-opacity-75">
                        <li class="mr-2">
                            <a href="<?= base_url() ?>" class="hover:text-white transition-colors duration-300">Home</a>
                        </li>
                        <li class="mr-2">/</li>
                        <li class="mr-2">
                            <a href="<?= base_url('bantuan-sosial') ?>" class="hover:text-white transition-colors duration-300">Bantuan Sosial</a>
                        </li>
                        <li class="mr-2">/</li>
                        <li class="text-white">Detail</li>
                    </ol>
                </nav>

                <h1 class="text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                    <?= $penerimaan_blt->deskripsi ?>
                </h1>
                
                <div class="space-y-3 text-white text-opacity-90">
                    <p class="text-lg flex items-center">
                        <i class="fas fa-money-bill-wave mr-3"></i>
                        Nominal: Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?>
                    </p>
                    <p class="text-lg flex items-center">
                        <i class="fas fa-calendar mr-3"></i>
                        Dibuat: <?= date('d F Y', strtotime($penerimaan_blt->tanggal_dibuat)) ?>
                    </p>
                </div>
            </div>
            <div class="lg:w-1/3 text-center">
                <div class="inline-block p-8 bg-white bg-opacity-10 rounded-full">
                    <i class="fas fa-users text-6xl text-white"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Summary Statistics -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="group">
                    <div class="text-4xl font-bold text-primary mb-2 group-hover:scale-110 transition-transform duration-300">
                        <?= count($penerima) ?>
                    </div>
                    <p class="text-gray-600 font-medium">Total Penerima Terverifikasi</p>
                </div>
                <div class="group">
                    <div class="text-4xl font-bold text-green-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                        Rp <?= number_format($penerimaan_blt->nominal * count($penerima), 0, ',', '.') ?>
                    </div>
                    <p class="text-gray-600 font-medium">Total Dana Tersalurkan</p>
                </div>
                <div class="group">
                    <div class="text-4xl font-bold text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                        Rp <?= number_format($penerimaan_blt->nominal, 0, ',', '.') ?>
                    </div>
                    <p class="text-gray-600 font-medium">Nominal per Penerima</p>
                </div>
            </div>
        </div>

        <!-- Recipients List -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="bg-primary text-white p-6">
                <h2 class="text-2xl font-bold flex items-center">
                    <i class="fas fa-list mr-3"></i>
                    Daftar Penerima Bantuan Sosial
                </h2>
            </div>
            
            <div class="p-6">
                <?php if (!empty($penerima)): ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-4 px-4 font-semibold text-gray-700 w-16">No</th>
                                    <th class="text-left py-4 px-4 font-semibold text-gray-700">NIK</th>
                                    <th class="text-left py-4 px-4 font-semibold text-gray-700">Nama</th>
                                    <th class="text-left py-4 px-4 font-semibold text-gray-700">Alamat</th>
                                    <th class="text-left py-4 px-4 font-semibold text-gray-700">RT/RW</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($penerima as $key => $value): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                                        <td class="py-4 px-4 text-gray-600"><?= $key + 1 ?></td>
                                        <td class="py-4 px-4">
                                            <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                                                <?= $value->nik ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-semibold text-gray-800"><?= $value->nama ?></span>
                                        </td>
                                        <td class="py-4 px-4 text-gray-600"><?= $value->alamat ?></td>
                                        <td class="py-4 px-4">
                                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                                RT <?= $value->rt ?> / RW <?= $value->rw ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <!-- Empty State -->
                    <div class="text-center py-16">
                        <div class="inline-block p-8 bg-gray-100 rounded-full mb-6">
                            <i class="fas fa-users-slash text-6xl text-gray-400"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Belum Ada Penerima Terverifikasi</h3>
                        <p class="text-gray-600 max-w-md mx-auto">
                            Saat ini belum ada penerima yang terverifikasi untuk program bantuan sosial ini.
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Info Card -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mt-8">
            <div class="flex flex-col lg:flex-row items-center">
                <div class="lg:w-2/3 mb-6 lg:mb-0">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-primary mr-3"></i>
                        Catatan Penting
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Data yang ditampilkan adalah penerima bantuan sosial yang telah melalui proses verifikasi dan dinyatakan layak menerima bantuan. 
                        Jika ada pertanyaan atau keberatan, silakan hubungi kantor desa.
                    </p>
                </div>
                <div class="lg:w-1/3 text-center lg:text-right space-y-3 lg:space-y-0 lg:space-x-3">
                    <a href="<?= base_url('bantuan-sosial') ?>" 
                       class="inline-block border border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition-colors duration-300 font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Kembali
                    </a>
                    <a href="<?= base_url('guestbook') ?>" 
                       class="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors duration-300 font-medium">
                        <i class="fas fa-phone mr-2"></i>
                        Hubungi Kami
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bg-primary {
    background-color: #3B82F6;
}

.bg-primary-dark {
    background-color: #2563EB;
}

.text-primary {
    color: #3B82F6;
}

.border-primary {
    border-color: #3B82F6;
}

.bg-secondary {
    background-color: #8B5CF6;
}

.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .text-4xl {
        font-size: 2rem;
    }
    
    .text-5xl {
        font-size: 2.5rem;
    }
    
    .text-6xl {
        font-size: 3rem;
    }
    
    table {
        font-size: 0.9rem;
    }
}
</style>
