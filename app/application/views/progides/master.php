<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Sistem Informasi Desa' ?></title>

    <!-- Meta Tags -->
    <meta name="description" content="<?= isset($setting->deskripsi) ? $setting->deskripsi : 'Sistem Informasi Digital Desa' ?>">
    <meta name="keywords" content="desa, pemerintahan, informasi, digital">
    <meta name="author" content="ProGides">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= isset($title) ? $title : 'Sistem Informasi Desa' ?>">
    <meta property="og:description" content="<?= isset($setting->deskripsi) ? $setting->deskripsi : 'Sistem Informasi Digital Desa' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= current_url() ?>">
    <?php if (isset($setting->logo_desa)) : ?>
        <meta property="og:image" content="<?= asset_url($setting->logo_desa) ?>">
    <?php endif; ?>

    <!-- Favicon -->
    <?php if (isset($setting->logo_desa)) : ?>
        <link rel="icon" type="image/png" href="<?= asset_url($setting->logo_desa) ?>">
    <?php endif; ?>

    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?= asset_url() ?>assets/templatesv3/css/progides.css" rel="stylesheet">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6e9e28',
                        'primary-dark': '#5a7f20',
                        'primary-light': '#8bb83a'
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-transparent fixed w-full top-0 z-50" id="navbar">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-white rounded-lg mr-3 flex items-center justify-center shadow-sm">
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" class="w-8 h-8 object-contain">
                        <?php else : ?>
                            <span class="text-primary font-bold text-lg">P</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-xl font-bold text-white">PROGIDES</span>
                        <span class="text-xs text-gray-200 hidden sm:block">Sistem Informasi Desa</span>
                    </div>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:flex items-center space-x-1">
                    <a href="<?= base_url() ?>"
                        class="nav-link <?= uri_string() == '' ? 'active' : '' ?> px-4 py-2 rounded-lg text-white <?= uri_string() == '' ? 'bg-primary bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10' ?> font-medium transition duration-300">
                        <i class="fas fa-home mr-2"></i>Beranda
                    </a>
                    <a href="<?= base_url('pemerintah') ?>"
                        class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300">
                        <i class="fas fa-users mr-2"></i>Pemerintahan
                    </a>
                    <a href="<?= base_url('infografi') ?>"
                        class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300">
                        <i class="fas fa-chart-bar mr-2"></i>Infografis
                    </a>
                    <a href="<?= base_url('belanja') ?>"
                        class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300">
                        <i class="fas fa-shopping-cart mr-2"></i>Belanja
                    </a>
                    <a href="<?= base_url('produk/hukum') ?>"
                        class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300">
                        <i class="fas fa-gavel mr-2"></i>Produk Hukum
                    </a>
                    <a href="<?= base_url('berita/all') ?>"
                        class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300">
                        <i class="fas fa-newspaper mr-2"></i>Berita
                    </a>

                    <!-- Dropdown Menu -->
                    <div class="relative group">
                        <button
                            class="nav-link px-4 py-2 rounded-lg text-white hover:bg-white hover:bg-opacity-10 font-medium transition duration-300 flex items-center">
                            <i class="fas fa-ellipsis-h mr-2"></i>Lainnya
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div
                            class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-2">
                                <a href="<?= base_url('bantuan-sosial') ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition duration-300">
                                    <i class="fas fa-hands-helping mr-2 text-primary"></i>Bantuan Sosial
                                </a>
                                <a href="<?= base_url('pengelolaandana') ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition duration-300">
                                    <i class="fas fa-chart-pie mr-2 text-primary"></i>Pengelolaan APB
                                </a>
                                <a href="<?= base_url('surat') ?>"
                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition duration-300">
                                    <i class="fas fa-envelope mr-2 text-primary"></i>Surat
                                </a>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <div class="lg:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-gray-200 transition duration-300 p-2">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu"
                class="lg:hidden hidden bg-white bg-opacity-95 backdrop-blur-sm rounded-lg mt-2 shadow-lg">
                <div class="py-4 px-4 space-y-2">
                    <a href="<?= base_url() ?>"
                        class="block px-4 py-3 <?= uri_string() == '' ? 'text-primary bg-primary bg-opacity-10' : 'text-gray-700 hover:bg-gray-100' ?> rounded-lg font-medium">
                        <i class="fas fa-home mr-3"></i>Beranda
                    </a>
                    <a href="<?= base_url('pemerintah') ?>"
                        class="block px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg font-medium transition duration-300">
                        <i class="fas fa-users mr-3"></i>Pemerintahan
                    </a>
                    <a href="<?= base_url('infografi') ?>"
                        class="block px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg font-medium transition duration-300">
                        <i class="fas fa-chart-bar mr-3"></i>Infografis
                    </a>
                    <a href="<?= base_url('belanja') ?>"
                        class="block px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg font-medium transition duration-300">
                        <i class="fas fa-shopping-cart mr-3"></i>Belanja
                    </a>
                    <a href="<?= base_url('produk/hukum') ?>"
                        class="block px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg font-medium transition duration-300">
                        <i class="fas fa-gavel mr-3"></i>Produk Hukum
                    </a>
                    <a href="<?= base_url('berita/all') ?>"
                        class="block px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg font-medium transition duration-300">
                        <i class="fas fa-newspaper mr-3"></i>Berita
                    </a>
                    <a href="<?= base_url('bantuan-sosial') ?>"
                        class="block px-4 py-3 <?= strpos(uri_string(), 'bantuan-sosial') !== false ? 'text-primary bg-primary bg-opacity-10' : 'text-gray-700 hover:bg-gray-100' ?> rounded-lg font-medium transition duration-300">
                        <i class="fas fa-hands-helping mr-3"></i>Bantuan Sosial
                    </a>
                    <a href="<?= base_url('pengelolaandana') ?>"
                        class="block px-4 py-3 <?= strpos(uri_string(), 'pengelolaandana') !== false ? 'text-primary bg-primary bg-opacity-10' : 'text-gray-700 hover:bg-gray-100' ?> rounded-lg font-medium transition duration-300">
                        <i class="fas fa-chart-pie mr-3"></i>Pengelolaan APB
                    </a>
                    <a href="<?= base_url('surat') ?>"
                        class="block px-4 py-3 <?= strpos(uri_string(), 'surat') !== false ? 'text-primary bg-primary bg-opacity-10' : 'text-gray-700 hover:bg-gray-100' ?> rounded-lg font-medium transition duration-300">
                        <i class="fas fa-envelope mr-3"></i>Surat
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <?php if (isset($content)): ?>
        <?php $this->load->view($content); ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="bg-gray-100 py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Logo and Village Info -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-primary rounded mr-3 flex items-center justify-center">
                            <?php if (isset($setting->logo_desa)) : ?>
                                <img src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" class="w-6 h-6 object-contain">
                            <?php else : ?>
                                <span class="text-white font-bold text-sm">P</span>
                            <?php endif; ?>
                        </div>
                        <span class="text-gray-800 font-bold text-lg">PROGIDES</span>
                    </div>
                    <h3 class="text-gray-800 font-bold text-lg mb-2">
                        <?= isset($setting->desa) ? 'Pemerintah ' . $setting->desa : 'Pemerintah Desa Kandangan Baru' ?>
                    </h3>
                    <p class="text-gray-600 text-sm">
                        <?= isset($setting->kecamatan) && isset($setting->kabupaten) && isset($setting->provinsi) ?
                            'Kec. ' . $setting->kecamatan . ' Kab. ' . $setting->kabupaten . ' Prov. ' . $setting->provinsi :
                            'Kab. Hulu Sungai Selatan Prov. Kalimantan Selatan' ?>
                    </p>
                </div>

                <!-- Address -->
                <div>
                    <h4 class="text-gray-800 font-semibold mb-4">Alamat</h4>
                    <p class="text-gray-600 text-sm">
                        <?= isset($setting->alamat) ? $setting->alamat : 'Jalan Kebun Karet No. 045' ?>
                    </p>

                    <h4 class="text-gray-800 font-semibold mt-6 mb-4">Kontak Penting</h4>
                    <div class="space-y-1 text-sm text-gray-600">
                        <?php if (isset($setting->telepon)) : ?>
                            <p>Kelurahan: <?= $setting->telepon ?></p>
                        <?php else : ?>
                            <p>Kelurahan: 081250525252</p>
                        <?php endif; ?>
                        <p>BPD DESA: 081255320374</p>
                        <p>Sekretaris Desa: 082251033003</p>
                        <p><?= isset($setting->email) ? $setting->email : '<EMAIL>' ?></p>
                    </div>
                </div>

                <!-- Sitemap -->
                <div>
                    <h4 class="text-gray-800 font-semibold mb-4">Sitemap</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="<?= base_url() ?>"
                                class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Beranda</a></li>
                        <li><a href="<?= base_url('pemerintah') ?>"
                                class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Pemerintahan</a></li>
                        <li><a href="<?= base_url('infografi') ?>"
                                class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Infografis</a></li>
                        <li><a href="<?= base_url('belanja') ?>"
                                class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Belanja</a></li>
                        <li><a href="<?= base_url('berita/all') ?>"
                                class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Berita</a></li>
                        <li><a href="<?= base_url('surat') ?>" class="text-gray-600 hover:text-primary transition-colors duration-300">•
                                Surat</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Navbar scroll effect
            const navbar = document.getElementById('navbar');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.remove('bg-transparent');
                    navbar.classList.add('bg-primary', 'shadow-lg', 'backdrop-blur-sm');
                } else {
                    navbar.classList.add('bg-transparent');
                    navbar.classList.remove('bg-primary', 'shadow-lg', 'backdrop-blur-sm');
                }
            });

            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Set active navigation based on current URL
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            const mobileLinks = document.querySelectorAll('#mobile-menu a');

            // Handle desktop nav links
            navLinks.forEach(link => {
                // Remove existing active classes
                link.classList.remove('active', 'bg-primary', 'bg-opacity-20');
                link.classList.add('hover:bg-white', 'hover:bg-opacity-10');

                const href = link.getAttribute('href');
                if (href) {
                    const linkPath = new URL(href, window.location.origin).pathname;

                    // Check if current path matches link path
                    if ((currentPath === '/' || currentPath === '/dockertest/' || currentPath.endsWith('/dockertest')) &&
                        (linkPath === '/' || linkPath.endsWith('/dockertest') || linkPath.endsWith('/dockertest/'))) {
                        // Home page
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    } else if (currentPath.includes('pemerintah') && href.includes('pemerintah')) {
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    } else if (currentPath.includes('infografi') && href.includes('infografi')) {
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    } else if (currentPath.includes('belanja') && href.includes('belanja')) {
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    } else if (currentPath.includes('produk') && href.includes('produk')) {
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    } else if (currentPath.includes('berita') && href.includes('berita')) {
                        link.classList.add('active', 'bg-primary', 'bg-opacity-20');
                        link.classList.remove('hover:bg-white', 'hover:bg-opacity-10');
                    }
                }
            });

            // Handle mobile nav links
            mobileLinks.forEach(link => {
                // Remove existing active classes
                link.classList.remove('text-primary', 'bg-primary', 'bg-opacity-10');
                link.classList.add('text-gray-700', 'hover:bg-gray-100');

                const href = link.getAttribute('href');
                if (href) {
                    const linkPath = new URL(href, window.location.origin).pathname;

                    // Check if current path matches link path
                    if ((currentPath === '/' || currentPath === '/dockertest/' || currentPath.endsWith('/dockertest')) &&
                        (linkPath === '/' || linkPath.endsWith('/dockertest') || linkPath.endsWith('/dockertest/'))) {
                        // Home page
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    } else if (currentPath.includes('pemerintah') && href.includes('pemerintah')) {
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    } else if (currentPath.includes('infografi') && href.includes('infografi')) {
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    } else if (currentPath.includes('belanja') && href.includes('belanja')) {
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    } else if (currentPath.includes('produk') && href.includes('produk')) {
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    } else if (currentPath.includes('berita') && href.includes('berita')) {
                        link.classList.add('text-primary', 'bg-primary', 'bg-opacity-10');
                        link.classList.remove('text-gray-700', 'hover:bg-gray-100');
                    }
                }
            });
        });
    </script>
</body>

</html>