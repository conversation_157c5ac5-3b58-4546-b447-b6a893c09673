<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?= isset($title) ? $title : 'Default Title' ?> - <?= isset($setting->desa) ? 'Desa ' . $setting->desa : null ?></title>
    <meta name="description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
    <meta name="keywords" content="<?= isset($setting->keywords) ? $setting->keywords : 'platform desa digital, portal desa digital, aplikasi digital desa, desa digital adalah, bip desa, digital desa, digital desa online, aplikasi pelayanan desa online, kriteria desa digital, layanan desa, login desa digital, web pelayanan desa, online digital desa, profil desa digital, rab desa digital, smart desa digital, download portal desa digital, website desa online, cara membuat desa digital, program desa digital 2021, contoh program desa digital, membangun desa digital, sistem desa digital, desa digital di indonesia, program desa digital, proposal desa digital, desa wisata digital adalah, aplikasi desa digital, apa itu desa digital, anggaran desa digital, ayo bangun desa digital, artikel desa digital, arti desa digital, apa keuntungan kelemahan peluang dan ancaman dalam menerapkan desa digital, desa berbasis digital, desa wisata berbasis digital, bumdes desa digital, contoh desa digital, ciri ciri desa digital, cara login desa digital, desa cerdas digital, ekonomi digital desa, gambar desa digital, inovasi desa digital, jumlah desa digital di indonesia, daftar desa digital di indonesia, konsep desa digital, kelebihan dan kekurangan desa digital, kriteria desa digital, kategori desa digital, desa digital login, login page desa digital, latar belakang desa digital, logo desa digital, manfaat desa digital, membangun desa digital, maksud dan tujuan desa digital, memajukan pariwisata desa melalui media digital, sistem desa digital, syarat menjadi desa digital, sistem informasi desa digital, sosialisasi desa digital, desa digital terbaik, template desa digital, target desa digital, teknologi desa digital, website desa digital, desa wisata digital di indonesia desa digital 2022, program digitalisasi desa, surat desa otomatis, aplikasi surat menyurat desa otomatis, format surat desa, kode surat desa, kop surat desa doc, logo surat desa, penomoran surat desa, nomor surat otomatis, surat menyurat desa excel, tata naskah surat desa, nomor surat tugas desa, nomor agenda surat desa' ?>">
    <meta name="robots" content="index, follow">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Open Graph / Facebook -->
    <meta property="og:site_name" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <meta property="og:type" content="website">
    <?php if (!isset($berita)): ?>
        <meta property="og:url" content="<?= base_url() ?>">
        <meta property="og:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
        <meta property="og:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
        <meta property="og:image" content="">
    <?php elseif (isset($berita->id) && isset($berita->judul) && isset($berita->deskripsi)) : ?>
        <meta property="og:url" content="<?= base_url(uri_string() . '?id=' . $berita->id) ?>">
        <meta property="og:title" content="<?= $berita->judul ?>">
        <meta property="og:description" content="<?= extract_text($berita->deskripsi) ?>">
        <meta property="og:image" itemprop="image" content="<?= asset_url($berita->foto) ?>">
    <?php endif; ?>

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <?php if (!isset($berita)): ?>
        <meta property="twitter:url" content="<?= base_url() ?>">
        <meta property="twitter:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
        <meta property="twitter:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
        <meta property="twitter:image" content="">
    <?php elseif (isset($berita->id) && isset($berita->judul) && isset($berita->deskripsi)) : ?>
        <meta property="twitter:url" content="<?= base_url(uri_string() . '?id=' . $berita->id) ?>">
        <meta property="twitter:title" content="<?= $berita->judul ?>">
        <meta property="twitter:description" content="<?= extract_text($berita->deskripsi) ?>">
        <meta property="twitter:image" itemprop="image" content="<?= asset_url($berita->foto) ?>">
    <?php endif; ?>

    <link rel="stylesheet" href="<?= base_url() ?>assets/templatesv3/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= base_url() ?>assets/templatesv3/css/style.css">

    <style>
        /* Custom dropdown styles */
        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            min-width: 160px;
            padding: 0.5rem 0;
            margin: 0;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .15);
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .175);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.375rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: #1e2125;
            background-color: #e9ecef;
        }

        .dropdown {
            position: relative;
        }
    </style>

    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"
        integrity="sha512-tS3S5qG0BlhnQROyJXvNjeEM4UpMXHrQfTGmbQ1gKmelCxlSEBUaxhRBj/EFTzpbP4RVSrpEikbmdJobCvhE3g=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="canonical" href="<?= base_url(uri_string()) ?>">

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "GovernmentOrganization",
            "name": "<?= isset($setting->desa) ? $setting->desa : null ?>",
            "alternateName": "Go Digital Desa",
            "url": "<?= base_url() ?>",
            "logo": "https://gides.id/assets/img/favicon.png"
        }
    </script>

    <?php if (getPlatformName() == 'Gides') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-495ZLQJLPL"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-495ZLQJLPL');
        </script>
    <?php elseif (getPlatformName() == 'GidesManis') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-YYSG07EWM1"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-YYSG07EWM1');
        </script>
    <?php endif; ?>
</head>

<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex" href="<?= base_url() ?>">
                <?php if (isset($setting->logo_desa)) : ?>
                    <img src="<?= asset_url($setting->logo_desa) ?>" alt="">
                <?php endif; ?>

                <div class="d-flex flex-column ms-3">
                    <span class="m-0"><?= isset($setting->desa) ? $setting->desa : null ?></span>
                    <small class="m-0"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></small>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == '' ? 'active' : null ?>" href="<?= base_url() ?>">Home</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == 'pemerintah' ? 'active' : null ?>" href="<?= base_url('pemerintah') ?>">Pemerintahan</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == 'infografi' ? 'active' : null ?>" href="<?= base_url('infografi') ?>">Infografis</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == 'belanja' ? 'active' : null ?>" href="<?= base_url('belanja') ?>">Belanja</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == 'produk/hukum' ? 'active' : null ?>" href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= uri_string() == 'berita/all' ? 'active' : null ?>" href="<?= base_url('berita/all') ?>">Berita</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('surat') ?>">Surat</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('guestbook') ?>">Buku Tamu</a>
                    </li>

                    <li class="nav-item dropdown" id="lainnyaDropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" aria-expanded="false">
                            Lainnya
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="<?= base_url('bantuan-sosial') ?>">
                                    <i class="fas fa-hands-helping me-2"></i>Bantuan Sosial
                                </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <?php if (isset($content)): ?>
        <?php $this->load->view($content); ?>
    <?php endif; ?>

    <footer class="footer">
        <div class="container">
            <div class="d-flex justify-content-between">
                <div class="d-flex">
                    <div>
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img src="<?= asset_url($setting->logo_desa) ?>" alt="">
                        <?php endif; ?>
                    </div>

                    <div class="d-flex flex-column ms-3">
                        <span class="m-0"><?= isset($setting->desa) ? $setting->desa : null ?></span>
                        <small class="m-0"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></small>
                    </div>
                </div>

                <div>
                    <div class="mb-4">
                        <h4>Alamat</h4>
                        <p><?= isset($setting->alamat) ? $setting->alamat : null ?></p>
                    </div>

                    <div class="mb-4">
                        <h4>Kontak Penting</h4>
                        <?php foreach ($kontakpenting as $key => $value): ?>
                            <p><?= $value->nama ?>: <a href="tel:<?= $value->no_telp ?>" class="text-decoration-none"><?= $value->no_telp ?></a></p>
                        <?php endforeach; ?>
                        <p id="email_protection"><?= isset($setting->email) ? base64_encode($setting->email) : null ?></p>
                    </div>
                </div>

                <div>
                    <h4>Sitemap</h4>

                    <ul class="sitemap">
                        <li>
                            <a href="<?= base_url('pemerintah') ?>">Pemerintahan</a>
                        </li>

                        <li>
                            <a href="<?= base_url('infografi') ?>">Infografis</a>
                        </li>

                        <li>
                            <a href="<?= base_url('belanja') ?>">Belanja</a>
                        </li>

                        <li>
                            <a href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                        </li>

                        <li>
                            <a href="<?= base_url('berita/all') ?>">Berita</a>
                        </li>

                        <li>
                            <a href="<?= base_url('surat') ?>">Surat</a>
                        </li>

                        <li>
                            <a href="<?= base_url('guestbook') ?>">Buku Tamu</a>
                        </li>

                        <li>
                            <a href="<?= base_url('bantuan-sosial') ?>">Bantuan Sosial</a>
                        </li>
                    </ul>
                </div>

                <div>
                    <h4>Social Media</h4>

                    <ul class="social-media">
                        <li>
                            <a href="<?= isset($setting->facebook) ? validate_sosmedurl($setting->facebook, 'facebook') : 'javascript:;' ?>">
                                <i class="fab fa-facebook"></i>
                            </a>
                        </li>

                        <li>
                            <a href="<?= isset($setting->twitter) ? validate_sosmedurl($setting->twitter, 'twitter') : 'javascript:;' ?>">
                                <i class="fab fa-twitter"></i>
                            </a>
                        </li>

                        <li>
                            <a href="<?= isset($setting->instagram) ? validate_sosmedurl($setting->instagram, 'instagram') : 'javascript:;' ?>">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </li>

                        <li>
                            <a href="<?= isset($setting->youtube) ? validate_sosmedurl($setting->youtube, 'youtube') : 'javascript:;' ?>">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="container">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="mb-0">&copy; <?= date('Y') ?> by Go Digital Desa (<?= strtoupper(getPlatformName()) ?>). All Rights Reserved</p>
                    </div>

                    <div>
                        <ul>
                            <li>
                                <a href="javascript:;">Privacy Policy</a>
                            </li>

                            <li>
                                <a href="javascript:;">Terms of Service</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <div class="modal fade" id="modalContact" tabindex="-1" aria-labelledby="modalContactLabel" aria-hidden="true">
    </div>

    <div class="modal fade" id="modalStrukturOrganisasi" tabindex="-1" aria-labelledby="modalStrukturOrganisasiLabel"
        aria-hidden="true">
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
    <script src="<?= base_url() ?>assets/templatesv3/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"
        integrity="sha512-bPs7Ae6pVvhOSiIcyUClR7/q2OAsRiovw4vAkX+zJbw3ShAeeqezq50RIIcIURq7Oa20rW2n2q+fyXBNcU9lrw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.44.1/apexcharts.min.js"
        integrity="sha512-WHo2Eg23N0LUrkhx6YARZTlZX0t5fGnj8TR8c1/4f9cbxm8shrLV7GQlyuxBCtNA6kssq0DUcihcrmn1/F2H3Q=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/sweetalert/sweetalert.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/youtube-background/jquery.youtube-background.min.js"></script>
    <script src="<?= asset_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= asset_url() ?>assets/js/script.js"></script>
    <script src="<?= asset_url() ?>assets/templates/js/pie-chart.js"></script>

    <script>
        $('.product-slider, .wisata-slider').slick({
            arrows: false,
            slidesToShow: 3,
            slidesToScroll: 3,
            responsive: [{
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2
                    }
                },
                {
                    breakpoint: 576,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });

        $('.nav-tabs button').click(function() {
            $('.product-slider').slick('refresh');
        });

        $(window).scroll(function() {
            if ($(window).scrollTop() >= 100) {
                $('nav').addClass('bg-white');
            } else {
                $('nav').removeClass('bg-white');
            }
        });

        $.AjaxRequest('#frmKritikSaran', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.reload();
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        window.addEventListener('load', (event) => {
            setTraffic();

            var email = document.getElementById('email_protection');
            var email_address = atob(email.innerHTML);

            email.innerHTML = email_address;
        });

        function setTraffic() {
            $.ajax({
                url: "<?= base_url() . '/traffic/add' ?>",
                method: 'POST',
                dataType: 'json',
                success: function(response) {
                    console.log(response.MESSAGE);
                }
            });
        }

        jQuery(document).ready(function() {
            jQuery('[data-vbg]').youtube_background();
        });

        function orderNow(phonenumber) {
            $('#modalContact').html(`<div class="modal-dialog modal-dialog-centered custom-chat-modal">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="text-center">
                            <img src="<?= base_url('assets/templatesv3/images/chat.svg') ?>" alt="" width="200">
                        </div>

                        <h3 class="mt-4">Pesan Sekarang</h3>
                        <p>Hubungi kami untuk memesan produk ini. Kami siap membantu Anda!</p>

                        <b>Informasi Kontak:</b>
                        <ul>
                            <li>Nomor Telepon: ${phonenumber}</li>
                            <li>WhatsApp: ${phonenumber}</li>
                        </ul>

                        <div class="row mt-4">
                            <div class="col">
                                <a href="https://wa.me/${phonenumber}" class="btn btn-primary w-100" target="_blank">
                                    <i class="fab fa-whatsapp"></i>
                                    Chat via WhatsApp
                                </a>
                            </div>

                            <div class="col">
                                <a href="tel:${phonenumber}" class="btn btn-default w-100">
                                    <i class="fa fa-phone"></i>
                                    Telepon Sekarang
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`);
            $('#modalContact').modal('show');
        }

        // Custom dropdown handler for Lainnya menu
        $(document).ready(function() {
            $('#lainnyaDropdown .nav-link').on('click', function(e) {
                e.preventDefault();
                const dropdown = $(this).closest('.dropdown');
                const menu = dropdown.find('.dropdown-menu');

                // Toggle dropdown
                if (menu.hasClass('show')) {
                    menu.removeClass('show');
                    $(this).attr('aria-expanded', 'false');
                } else {
                    // Close other dropdowns first
                    $('.dropdown-menu.show').removeClass('show');
                    $('.dropdown-toggle[aria-expanded="true"]').attr('aria-expanded', 'false');

                    // Open this dropdown
                    menu.addClass('show');
                    $(this).attr('aria-expanded', 'true');
                }
            });

            // Close dropdown when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown').length) {
                    $('.dropdown-menu.show').removeClass('show');
                    $('.dropdown-toggle[aria-expanded="true"]').attr('aria-expanded', 'false');
                }
            });
        });
    </script>
</body>

</html>