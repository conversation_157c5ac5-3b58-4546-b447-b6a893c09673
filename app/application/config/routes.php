<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Welcome';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['disabled'] = 'Disabled';

$route['sitemap'] = 'Sitemap';
$route['sitemap\.xml'] = 'Sitemap';
$route['robots\.txt'] = 'Sitemap/robots';

$route['admin'] = 'Dashboard';
$route['admin/desa'] = 'Dashboard/desa';
$route['admin/pemerintah/download/kabupaten'] = 'Dashboard/download_pemerintah_kabupaten';
$route['admin/pemerintah/download/kecamatan/(:num)'] = 'Dashboard/download_pemerintah_kecamatan/$1';
$route['admin/perangkat'] = 'Dashboard/perangkat';
$route['admin/perangkat/log'] = 'Dashboard/perangkat_log';
$route['admin/datatables_pengaduan'] = 'Dashboard/datatables_pengaduan';
$route['admin/export_pengaduan'] = 'Dashboard/export_pengaduan';
$route['admin/pengaduan'] = 'Dashboard/pengaduan';

$route['auth/login'] = 'Auth';
$route['auth/login/process'] = 'Auth/process_login';
$route['auth/login/resetpassword'] = 'Auth/request_resetpassword';
$route['auth/reset-password'] = 'Auth/resetpassword';
$route['auth/reset-password/process'] = 'Auth/process_resetpassword';
$route['logout'] = 'Auth/logout';

$route['settingumum'] = 'SettingUmum';
$route['settingumum/process'] = 'SettingUmum/process_update';

$route['settingmap'] = 'SettingMap';
$route['settingmap/process'] = 'SettingMap/process_update';

$route['pemerintahan'] = 'Pemerintahan';
$route['pemerintahan/process'] = 'Pemerintahan/process_update';

$route['infografis'] = 'InfoGrafis';
$route['infografis/process'] = 'InfoGrafis/process_update';
$route['infografis/add'] = 'InfoGrafis/add';
$route['infografis/add/process'] = 'InfoGrafis/process_add';
$route['infografis/delete'] = 'InfoGrafis/process_delete';
$route['infografis/edit/(:num)'] = 'InfoGrafis/edit/$1';
$route['infografis/edit/(:num)/process'] = 'InfoGrafis/process_edit/$1';

$route['kontakpenting'] = 'KontakPenting';
$route['kontakpenting/datatables'] = 'KontakPenting/datatables';
$route['kontakpenting/add'] = 'KontakPenting/add';
$route['kontakpenting/add/process'] = 'KontakPenting/process_add';
$route['kontakpenting/delete'] = 'KontakPenting/process_delete';
$route['kontakpenting/edit/(:num)'] = 'KontakPenting/edit/$1';
$route['kontakpenting/edit/(:num)/process'] = 'KontakPenting/process_edit/$1';

$route['berita'] = 'Berita';
$route['berita/generate_description'] = 'Berita/generate_description';
$route['berita/datatables'] = 'Berita/datatables';
$route['berita/add'] = 'Berita/add';
$route['berita/add/process'] = 'Berita/process_add';
$route['berita/add/fixtitle'] = 'Berita/fixtitle';
$route['berita/delete'] = 'Berita/process_delete';
$route['berita/edit/(:num)'] = 'Berita/edit/$1';
$route['berita/edit/(:num)/process'] = 'Berita/process_edit/$1';
$route['berita/share'] = 'Berita/share';

$route['produk'] = 'Produk';
$route['produk/datatables'] = 'Produk/datatables';
$route['produk/add'] = 'Produk/add';
$route['produk/add/process'] = 'Produk/process_add';
$route['produk/delete'] = 'Produk/process_delete';
$route['produk/edit/(:num)'] = 'Produk/edit/$1';
$route['produk/edit/(:num)/process'] = 'Produk/process_edit/$1';
$route['produk/deksripsi'] = 'Produk/deskripsi';

$route['produkhukum'] = 'ProdukHukum';
$route['produkhukum/datatables'] = 'ProdukHukum/datatables';
$route['produkhukum/add'] = 'ProdukHukum/add';
$route['produkhukum/add/process'] = 'ProdukHukum/process_add';
$route['produkhukum/edit/(:num)'] = 'ProdukHukum/edit/$1';
$route['produkhukum/edit/(:num)/process'] = 'ProdukHukum/process_edit/$1';
$route['produkhukum/delete'] = 'ProdukHukum/process_delete';

$route['produk/hukum'] = 'ProdukHukum/produkhukum';
$route['produk/hukum/detail'] = 'ProdukHukum/detail';
$route['produk/hukum/detail/(:num)'] = 'ProdukHukum/detail_produkhukum/$1';
$route['produk/hukum/detail/download/(:num)'] = 'ProdukHukum/download_produkhukum/$1';

$route['wisata'] = 'Wisata';
$route['wisata/datatables'] = 'Wisata/datatables';
$route['wisata/add'] = 'Wisata/add';
$route['wisata/add/process'] = 'Wisata/process_add';
$route['wisata/delete'] = 'Wisata/process_delete';
$route['wisata/edit/(:num)'] = 'Wisata/edit/$1';
$route['wisata/edit/(:num)/process'] = 'Wisata/process_edit/$1';

$route['apbdesa'] = 'APBDesa';
$route['apbdesa/datatables'] = 'APBDesa/datatables';
$route['apbdesa/add'] = 'APBDesa/add';
$route['apbdesa/add/process'] = 'APBDesa/process_add';
$route['apbdesa/delete'] = 'APBDesa/process_delete';
$route['apbdesa/delete_year'] = 'APBDesa/process_delete_year';
$route['apbdesa/edit/(:num)'] = 'APBDesa/edit/$1';
$route['apbdesa/edit/(:num)/process'] = 'APBDesa/process_edit/$1';
$route['apbdesa/edit/(:num)/add'] = 'APBDesa/add_detail/$1';
$route['apbdesa/edit/(:num)/add/process'] = 'APBDesa/process_add_detail/$1';
$route['apbdesa/edit/(:num)/detail'] = 'APBDesa/edit_detail/$1';
$route['apbdesa/edit/(:num)/detail/process'] = 'APBDesa/process_edit_detail/$1';
$route['apbdesa/edit/(:num)/delete'] = 'APBDesa/process_delete_detail/$1';
$route['apbdesa/detail/bidang'] = 'APBDesa/detail_bidang';
$route['apbdesa/detail/subbidang'] = 'APBDesa/detail_subbidang';
$route['apbdesa/detail/kegiatan'] = 'APBDesa/detail_kegiatan';
$route['apbdesa/export'] = 'APBDesa/export';

$route['anggarankelurahan'] = 'APBDesa';
$route['anggarankelurahan/datatables'] = 'APBDesa/datatables';
$route['anggarankelurahan/add'] = 'APBDesa/add';
$route['anggarankelurahan/add/process'] = 'APBDesa/process_add';
$route['anggarankelurahan/delete'] = 'APBDesa/process_delete';
$route['anggarankelurahan/delete_year'] = 'APBDesa/process_delete_year';
$route['anggarankelurahan/edit/(:num)'] = 'APBDesa/edit/$1';
$route['anggarankelurahan/edit/(:num)/process'] = 'APBDesa/process_edit/$1';
$route['anggarankelurahan/edit/(:num)/add'] = 'APBDesa/add_detail/$1';
$route['anggarankelurahan/edit/(:num)/add/process'] = 'APBDesa/process_add_detail/$1';
$route['anggarankelurahan/edit/(:num)/detail'] = 'APBDesa/edit_detail/$1';
$route['anggarankelurahan/edit/(:num)/detail/process'] = 'APBDesa/process_edit_detail/$1';
$route['anggarankelurahan/edit/(:num)/delete'] = 'APBDesa/process_delete_detail/$1';
$route['anggarankelurahan/detail/bidang'] = 'APBDesa/detail_bidang';
$route['anggarankelurahan/detail/subbidang'] = 'APBDesa/detail_subbidang';
$route['anggarankelurahan/detail/kegiatan'] = 'APBDesa/detail_kegiatan';
$route['anggarankelurahan/export'] = 'APBDesa/export';

$route['wisata/(:num)'] = 'Wisata/view_wisata/$1';
$route['berita/(:num)/(:any)'] = 'Berita/view_berita/$1/$2';
$route['berita/all'] = 'Berita/view_all';
$route['berita/all/(:num)'] = 'Berita/view_all/$1';
$route['pengelolaandana'] = 'APBDesa/pengelolaandana';
$route['pengelolaandana/cetak'] = 'APBDesa/cetak';
$route['pemerintah'] = 'Pemerintahan/pemerintah';
$route['pemerintah/detail'] = 'Pemerintahan/detail_strukturorganisasi';
$route['belanja'] = 'Produk/belanja';
$route['belanja/(:num)'] = 'Produk/belanja/$1';
$route['belanja/detail'] = 'Produk/detail';
$route['infografi'] = 'InfoGrafis/infografi';

$route['manage/admin'] = 'ManageAdminController';
$route['manage/admin/set_status'] = 'ManageAdminController/set_status';
$route['manage/admin/add'] = 'ManageAdminController/add';
$route['manage/admin/add/process'] = 'ManageAdminController/process_add';
$route['manage/admin/delete'] = 'ManageAdminController/process_delete';
$route['manage/admin/edit/(:num)'] = 'ManageAdminController/edit/$1';
$route['manage/admin/edit/(:num)/process'] = 'ManageAdminController/process_edit/$1';
$route['manage/admin/royalty/(:num)'] = 'ManageAdminController/royalty/$1';
$route['manage/admin/royalty/(:num)/process'] = 'ManageAdminController/process_royalty/$1';
$route['manage/admin/bpd/(:num)'] = 'ManageAdminController/bpd/$1';
$route['manage/admin/bpd/(:num)/process'] = 'ManageAdminController/process_bpd/$1';
$route['manage/admin/bpd/(:num)/add'] = 'ManageAdminController/add_bpd/$1';
$route['manage/admin/bpd/(:num)/add/process'] = 'ManageAdminController/process_add_bpd/$1';
$route['manage/admin/bpd/(:num)/delete'] = 'ManageAdminController/process_delete_bpd/$1';
$route['manage/admin/bpd/(:num)/edit'] = 'ManageAdminController/edit_bpd/$1';
$route['manage/admin/bpd/(:num)/edit/process'] = 'ManageAdminController/process_edit_bpd/$1';
$route['manage/admin/surat/(:num)'] = 'ManageAdminController/surat/$1';
$route['manage/admin/surat/(:num)/process'] = 'ManageAdminController/process_surat/$1';
$route['manage/admin/surat/(:num)/model'] = 'ManageAdminController/model_surat/$1';
$route['manage/admin/surat/(:num)/model/process'] = 'ManageAdminController/process_model_surat/$1';
$route['manage/admin/surat/(:num)/letterscode'] = 'ManageAdminController/letterscode_surat/$1';
$route['manage/admin/surat/(:num)/letterscode/process'] = 'ManageAdminController/process_letterscode_surat/$1';
$route['manage/admin/customdomain/(:num)'] = 'ManageAdminController/customdomain/$1';
$route['manage/admin/customdomain/(:num)/process'] = 'ManageAdminController/process_customdomain/$1';
$route['manage/admin/customdomain/(:num)/request'] = 'ManageAdminController/request_customdomain/$1';
$route['manage/admin/subdistrict_account'] = 'ManageAdminController/subdistrict_account';

$route['manage/superadmin'] = 'SuperAdminController';
$route['manage/superadmin/add'] = 'SuperAdminController/add';
$route['manage/superadmin/add/process'] = 'SuperAdminController/process_add';
$route['manage/superadmin/edit/(:num)'] = 'SuperAdminController/edit/$1';
$route['manage/superadmin/edit/(:num)/process'] = 'SuperAdminController/process_edit/$1';
$route['manage/superadmin/delete'] = 'SuperAdminController/process_delete';

$route['manage/wablas'] = 'WABlasController';
$route['manage/wablas/update'] = "WABlasController/process_update";

$route['manage/whatsapp'] = 'WhatsAppController';
$route['manage/whatsapp/qr'] = 'WhatsAppController/qr';
$route['manage/whatsapp/check'] = 'WhatsAppController/check';
$route['manage/whatsapp/logout'] = 'WhatsAppController/logout';

$route['manage/prompt'] = 'PromptController';
$route['manage/prompt/process'] = 'PromptController/process_update';

$route['manage/pmd'] = 'PMDController';
$route['manage/pmd/add'] = 'PMDController/add';
$route['manage/pmd/add/process'] = 'PMDController/process_add';
$route['manage/pmd/delete'] = 'PMDController/process_delete';
$route['manage/pmd/edit/(:num)'] = 'PMDController/edit/$1';
$route['manage/pmd/edit/(:num)/process'] = 'PMDController/process_edit/$1';

$route['manage/subdistrict'] = 'SubdistrictController';
$route['manage/subdistrict/add'] = 'SubdistrictController/add';
$route['manage/subdistrict/add/process'] = 'SubdistrictController/process_add';
$route['manage/subdistrict/edit/(:num)'] = 'SubdistrictController/edit/$1';
$route['manage/subdistrict/edit/(:num)/process'] = 'SubdistrictController/process_edit/$1';
$route['manage/subdistrict/delete'] = 'SubdistrictController/process_delete';

$route['manage/regency'] = 'RegencyController';
$route['manage/regency/edit'] = 'RegencyController/edit';
$route['manage/regency/edit/process'] = 'RegencyController/process_edit';
$route['manage/regency/set_integration'] = 'RegencyController/set_integration';
// $route['manage/regency/set_siades'] = 'RegencyController/set_siades';

$route['manage/diskominfo'] = 'DiskominfoController';
$route['manage/diskominfo/add'] = 'DiskominfoController/add';
$route['manage/diskominfo/add/process'] = 'DiskominfoController/process_add';
$route['manage/diskominfo/delete'] = 'DiskominfoController/process_delete';
$route['manage/diskominfo/edit/(:num)'] = 'DiskominfoController/edit/$1';
$route['manage/diskominfo/edit/(:num)/process'] = 'DiskominfoController/process_edit/$1';

$route['warga'] = 'Warga';
$route['warga/datatables'] = 'Warga/datatables';
$route['warga/add'] = 'Warga/add';
$route['warga/add/process'] = 'Warga/process_add';
$route['warga/edit/(:num)'] = 'Warga/edit/$1';
$route['warga/edit/(:num)/process'] = 'Warga/process_edit/$1';
$route['warga/delete'] = 'Warga/process_delete';
$route['warga/delete/all'] = 'Warga/process_delete_all';
$route['warga/detail'] = 'Warga/detail';
$route['warga/import'] = 'Warga/importexcel';
$route['warga/import/bip'] = 'Warga/importexcel_bip';
$route['warga/import/bip/preview'] = 'Warga/preview_importexcel_bip';
$route['warga/import/bip/preview/approve'] = 'Warga/approve_importexcel_bip';
$route['warga/import/prodeskel'] = 'Warga/importexcel_prodeskel';
$route['warga/import/prodeskel/process'] = 'Warga/process_importexcel_prodeskel';
$route['warga/format/excel'] = 'Warga/format_excel';
$route['warga/rfid'] = 'Warga/rfid';
$route['warga/rfid/process'] = 'Warga/process_rfid';
$route['warga/export'] = 'Warga/process_export_warga';
$route['warga/datainvalid'] = 'Warga/datainvalid';
$route['warga/datainvalid/detail'] = 'Warga/datainvalid_detail';
$route['warga/datainvalid/detail/delete'] = 'Warga/process_delete';

$route['migrasi'] = 'Migrasi';
$route['migrasi/datatables'] = 'Migrasi/datatables';
$route['migrasi/add'] = 'Migrasi/add';
$route['migrasi/add/process'] = 'Migrasi/process_add';
$route['migrasi/delete'] = 'Migrasi/process_delete';
$route['migrasi/edit/(:num)'] = 'Migrasi/edit/$1';
$route['migrasi/edit/(:num)/process'] = 'Migrasi/process_edit/$1';
$route['migrasi/request'] = 'Migrasi/request';
$route['migrasi/approve'] = 'Migrasi/approve';
$route['migrasi/reject'] = 'Migrasi/reject';

$route['slider'] = 'Slider';
$route['slider/datatables'] = 'Slider/datatables';
$route['slider/add'] = 'Slider/add';
$route['slider/add/process'] = 'Slider/process_add';
$route['slider/delete'] = 'Slider/delete';

$route['kritiksaran'] = 'Welcome/process_kritiksaran';
$route['pengaduan'] = 'Welcome/pengaduan';
$route['guestbook'] = 'Welcome/guestbook';
$route['guestbook/process'] = 'Welcome/process_guestbook';
$route['guestbook/appointment/(:num)'] = 'Welcome/appointment/$1';

$route['notification'] = 'Notification';

$route['surat'] = 'Surat';
$route['surat/cek_nik'] = 'Surat/cek_nik';
$route['surat/create/(:num)/(:num)'] = 'Surat/create/$1/$2';
$route['surat/create/(:num)/(:num)/process'] = 'Surat/process_createv2';
$route['surat/create/(:num)/(:num)/fields'] = 'Surat/form_fields/$1';
$route['surat/done'] = 'Surat/done';
$route['surat/arsip/masuk'] = 'Surat/arsip_masuk';
$route['surat/arsip/masuk/add'] = 'Surat/add_arsip_masuk';
$route['surat/arsip/masuk/add/process'] = 'Surat/process_add_arsip_masuk';
$route['surat/arsip/masuk/delete'] = 'Surat/process_delete_arsip_masuk';
$route['surat/arsip/masuk/edit/(:num)'] = 'Surat/edit_arsip_masuk/$1';
$route['surat/arsip/masuk/edit/(:num)/process'] = 'Surat/process_edit_arsip_masuk/$1';
$route['surat/arsip/keluar'] = 'Surat/permohonan_baru';
$route['surat/arsip/keluar/set_approve'] = 'Surat/set_approve';
$route['surat/arsip/keluar/detail'] = 'Surat/permohonan_baru_detail';
$route['surat/arsip/keluar/detail/datatables'] = 'Surat/datatables_permohonan_baru';
$route['surat/arsip/keluar/add'] = 'Surat/add_arsip_keluar';
$route['surat/arsip/keluar/add/process'] = 'Surat/process_add_arsip_keluar';

$route['surat/permohonan/baru/set_approve'] = 'Surat/set_approve';
$route['surat/permohonan/baru/id/(:num)'] = 'Surat/permohonan/$1';
$route['surat/permohonan/baru/approve_digital'] = 'Surat/approve_digital';
$route['surat/permohonan/baru/approve_digital/sign'] = 'Surat/approve_digital_sign';

$route['surat/cetak/(:num)'] = 'Surat/cetak/$1';
$route['surat/switch'] = 'Surat/switch_mode';
$route['surat/nikah/n2'] = 'Surat/cetak_nikah_n2';
$route['surat/nikah/n3'] = 'Surat/cetak_nikah_n3';
$route['surat/nikah/wali_nikah'] = 'Surat/cetak_wali_nikah';
$route['surat/nikah/n4'] = 'Surat/cetak_nikah_n4';
$route['surat/nikah/n6'] = 'Surat/cetak_nikah_n6';
$route['surat/nikah/data_calon'] = 'Surat/cetak_calon';
$route['surat/nikah/catin'] = 'Surat/cetak_catin';
$route['surat/sk/penghasilan'] = 'Surat/cetak_sk_penghasilan';
$route['surat/sk/skck'] = 'Surat/cetak_sk_skck';
$route['surat/sk/domisili'] = 'Surat/cetak_sk_domisili';
$route['surat/delete'] = 'Surat/delete';
$route['surat/check/nik'] = 'Surat/check_nik';
$route['surat/preview/(:num)'] = 'Surat/preview/$1';

$route['kategori_infografis'] = 'Kategori_InfoGrafis';
$route['kategori_infografis/datatables'] = 'Kategori_InfoGrafis/datatables';
$route['kategori_infografis/add'] = 'Kategori_InfoGrafis/add';
$route['kategori_infografis/add/process'] = 'Kategori_InfoGrafis/process_add';
$route['kategori_infografis/delete'] = 'Kategori_InfoGrafis/process_delete';
$route['kategori_infografis/edit/(:num)'] = 'Kategori_InfoGrafis/edit/$1';
$route['kategori_infografis/edit/(:num)/process'] = 'Kategori_InfoGrafis/process_edit/$1';

$route['changeprofile'] = 'Account/changepassword';
$route['changeprofile/process'] = 'Account/process_changepassword';
$route['changeprofile/profile'] = 'Account/process_profile';

$route['guest/product_detail/(:num)'] = 'Produk/product_detail/$1';

$route['kategori_surat'] = 'KategoriSurat';
$route['kategori_surat/detail/(:num)'] = 'KategoriSurat/detail/$1';
$route['kategori_surat/detail/(:num)/process'] = 'KategoriSurat/process_add_detail/$1';
$route['kategori_surat/detail/(:num)/delete'] = 'KategoriSurat/process_delete_detail';
$route['kategori_surat/letterscode'] = 'KategoriSurat/letterscode';
$route['kategori_surat/letterscode/process'] = 'KategoriSurat/process_letterscode';
$route['kategori_surat/formatsurat'] = 'KategoriSurat/formatsurat';
$route['kategori_surat/formatsurat/process'] = 'KategoriSurat/process_formatsurat';

$route['api/users/kabupaten'] = 'Users/api_kabupaten';
$route['api/users/kecamatan'] = 'Users/api_kecamatan';
$route['api/users/desa'] = 'Users/api_desa';

$route['api/surat/update'] = 'Surat/api_approve';
$route['api/surat/get'] = 'Surat/api_get';
$route['api/surat/create'] = 'Surat/api_create';

$route['api/warga/get'] = 'Warga/api_get';
$route['api/warga/create'] = 'Warga/api_create';
$route['api/warga/sync'] = 'Warga/api_sync';
$route['api/warga/get/detail'] = 'Warga/api_get_detail';

$route['api/desa/login'] = 'Desa/api_login';
$route['api/desa/logo/download'] = 'Desa/api_logo_download';

// Routes untuk Penerimaan BLT
$route['penerimaan_blt'] = 'Penerimaan_BLT/index';
$route['penerimaan_blt/add'] = 'Penerimaan_BLT/add';
$route['penerimaan_blt/edit/(:num)'] = 'Penerimaan_BLT/edit/$1';
$route['penerimaan_blt/detail/(:num)'] = 'Penerimaan_BLT/detail/$1';
$route['penerimaan_blt/datatables'] = 'Penerimaan_BLT/datatables';
$route['penerimaan_blt/get_warga'] = 'Penerimaan_BLT/get_warga';
$route['penerimaan_blt/get_selected_warga/(:num)'] = 'Penerimaan_BLT/get_selected_warga/$1';
$route['penerimaan_blt/process_add'] = 'Penerimaan_BLT/process_add';
$route['penerimaan_blt/process_edit/(:num)'] = 'Penerimaan_BLT/process_edit/$1';
$route['penerimaan_blt/delete'] = 'Penerimaan_BLT/process_delete';
$route['penerimaan_blt/update_verifikasi'] = 'Penerimaan_BLT/update_verifikasi';

// Routes untuk Bantuan Sosial (Public)
$route['bantuan-sosial'] = 'BantuanSosial/index';
$route['bantuan-sosial/detail/(:num)'] = 'BantuanSosial/detail/$1';

$route['api/penandatangan'] = 'Penandatangan/api_get';

$route['api/desktop/version'] = 'Desktop/api_version';
$route['api/desktop/download'] = 'Desktop/api_download';

$route['api/surat/sync'] = 'Surat/api_sync';

$route['api/send/panic'] = 'Panic/api_send';

$route['api/document/convertdocxtopdf'] = 'Document/api_convertdocxtopdf';

$route['api/news/villages/in'] = 'News/api_villages_in';

$route['api/product/category'] = 'Produk/api_category';
$route['api/product/latest'] = 'Produk/api_latest';
$route['api/product/all'] = 'Produk/api_all';

$route['traffic/add'] = 'TrafficController/add';
$route['traffic/report/daily'] = 'TrafficController/report_daily';
$route['traffic/report/monthly'] = 'TrafficController/report_monthly';
$route['traffic/report/yearly'] = 'TrafficController/report_yearly';

$route['master/select/kabupaten'] = 'MasterController/select_kabupaten';
$route['master/select/kecamatan'] = 'MasterController/select_kecamatan';
$route['master/select/kecamatandesa'] = 'MasterController/select_kecamatandesa';
$route['master/select/kelurahan'] = 'MasterController/select_kelurahan';
$route['master/select/kelurahandesa'] = 'MasterController/select_kelurahandesa';

$route['master/bidangpengaduan'] = 'BidangPengaduan';
$route['master/bidangpengaduan/datatables'] = 'BidangPengaduan/datatables';

$route['master/kategoridokumen'] = 'KategoriDokumen';
$route['master/kategoridokumen/datatables'] = 'KategoriDokumen/datatables';

$route['master/bidangperangkat'] = 'BidangPerangkat';
$route['master/bidangperangkat/add'] = 'BidangPerangkat/add';
$route['master/bidangperangkat/add/process'] = 'BidangPerangkat/process_add';
$route['master/bidangperangkat/delete'] = 'BidangPerangkat/process_delete';
$route['master/bidangperangkat/edit/(:num)'] = 'BidangPerangkat/edit/$1';
$route['master/bidangperangkat/edit/(:num)/process'] = 'BidangPerangkat/process_edit/$1';

$route['manage/category/letters'] = 'CategoryLetters/index';
$route['manage/category/letters/add'] = 'CategoryLetters/add';
$route['manage/category/letters/add/process'] = 'CategoryLetters/process_add';
$route['manage/category/letters/edit/(:num)'] = 'CategoryLetters/edit/$1';
$route['manage/category/letters/edit/(:num)/process'] = 'CategoryLetters/process_edit/$1';
$route['manage/category/letters/delete'] = 'CategoryLetters/process_delete';
$route['manage/category/letters/models/(:num)'] = 'CategoryLetters/models/$1';
$route['manage/category/letters/models/(:num)/process'] = 'CategoryLetters/process_models/$1';
$route['manage/category/letters/models/(:num)/builder/(:num)'] = 'CategoryLetters/builder/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/process'] = 'CategoryLetters/process_edit_builder/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/full'] = 'CategoryLetters/full_builder/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/full/preview'] = 'CategoryLetters/preview_builder/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/full/save'] = 'CategoryLetters/process_edit_format/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/full/fields/edit'] = 'CategoryLetters/edit_default_value/$1/$2';
$route['manage/category/letters/models/(:num)/builder/(:num)/full/fields/edit/process'] = 'CategoryLetters/process_edit_value/$1/$2';
$route['manage/category/letters/models/(:num)/delete'] = 'CategoryLetters/process_delete_builder/$1';
$route['manage/category/letters/models/(:num)/fields/(:num)'] = 'CategoryLetters/fields/$1/$2';
$route['manage/category/letters/models/(:num)/fields/(:num)/edit'] = 'CategoryLetters/edit_fields/$1/$2';
$route['manage/category/letters/models/(:num)/fields/(:num)/edit/process'] = 'CategoryLetters/process_edit_fields/$1/$2';
$route['manage/category/letters/models/(:num)/fields/(:num)/process'] = 'CategoryLetters/process_add_fields/$1/$2';
$route['manage/category/letters/models/(:num)/fields/(:num)/delete'] = 'CategoryLetters/process_delete_fields/$1/$2';
$route['manage/category/letters/models/(:num)/edit'] = 'CategoryLetters/edit_format_surat/$1';
$route['manage/category/letters/models/(:num)/edit/process'] = 'CategoryLetters/process_edit_format_surat/$1';
$route['manage/category/letters/models/(:num)/preview/(:num)'] = 'CategoryLetters/preview/$1/$2';

$route['manage/category/news'] = 'CategoryNews/index';
$route['manage/category/news/add'] = 'CategoryNews/add';
$route['manage/category/news/add/process'] = 'CategoryNews/process_add';
$route['manage/category/news/edit/(:num)'] = 'CategoryNews/edit/$1';
$route['manage/category/news/edit/(:num)/process'] = 'CategoryNews/process_edit/$1';
$route['manage/category/news/delete'] = 'CategoryNews/process_delete';

$route['manage/category/budgets'] = 'CategoryBudgets/index';
$route['manage/category/budgets/add'] = 'CategoryBudgets/add';
$route['manage/category/budgets/add/process'] = 'CategoryBudgets/process_add';
$route['manage/category/budgets/edit/(:num)'] = 'CategoryBudgets/edit/$1';
$route['manage/category/budgets/edit/(:num)/process'] = 'CategoryBudgets/process_edit/$1';
$route['manage/category/budgets/delete'] = 'CategoryBudgets/process_delete';
$route['manage/category/budgets/detail/(:num)'] = 'CategoryBudgets/detail/$1';
$route['manage/category/budgets/detail/(:num)/process'] = 'CategoryBudgets/process_add_detail/$1';
$route['manage/category/budgets/detail/(:num)/delete'] = 'CategoryBudgets/process_delete_detail/$1';
$route['manage/category/budgets/detail/(:num)/edit'] = 'CategoryBudgets/edit_detail/$1';
$route['manage/category/budgets/detail/(:num)/edit/process'] = 'CategoryBudgets/process_edit_detail/$1';
$route['manage/category/budgets/detail/(:num)/sub'] = 'CategoryBudgets/sub_detail/$1';
$route['manage/category/budgets/detail/(:num)/sub/process'] = 'CategoryBudgets/process_add_sub_detail/$1';
$route['manage/category/budgets/detail/(:num)/sub/delete'] = 'CategoryBudgets/process_delete_sub_detail/$1';
$route['manage/category/budgets/detail/(:num)/sub/update'] = 'CategoryBudgets/process_update_sub_detail/$1';
$route['manage/category/budgets/import'] = 'CategoryBudgets/importexcel';
$route['manage/category/budgets/import/process'] = 'CategoryBudgets/process_importexcel';
$route['manage/category/budgets/import/format/excel'] = 'CategoryBudgets/format_excel';

$route['manage/news/notification'] = 'News/notification';
$route['manage/news/notification/add'] = 'News/add_notification';
$route['manage/news/notification/add/process'] = 'News/process_add_notification';
$route['manage/news/notification/edit/(:num)'] = 'News/edit_notification/$1';
$route['manage/news/notification/edit/(:num)/process'] = 'News/process_edit_notification/$1';
$route['manage/news/notification/delete'] = 'News/process_delete_notification';

$route['manage/news'] = 'News/index';
$route['manage/news/datatables'] = 'News/datatables';
$route['manage/news/edit/(:num)'] = 'Berita/edit/$1';
$route['manage/news/edit/(:num)/process'] = 'Berita/process_edit/$1';
$route['manage/news/delete'] = 'Berita/process_delete';
$route['manage/news/verify'] = 'News/verify';
$route['manage/news/reject'] = 'News/reject';

$route['manage/slider_popup'] = 'SliderPopup';
$route['manage/slider_popup/add'] = 'SliderPopup/add';
$route['manage/slider_popup/add/process'] = 'SliderPopup/process_add';
$route['manage/slider_popup/delete'] = 'SliderPopup/process_delete';

$route['asset'] = 'AssetController';
$route['asset/datatables'] = 'AssetController/datatables';
$route['asset/add'] = 'AssetController/add';
$route['asset/add/process'] = 'AssetController/process_add';
$route['asset/edit/(:num)'] = 'AssetController/edit/$1';
$route['asset/edit/(:num)/process'] = 'AssetController/process_edit/$1';
$route['asset/delete'] = 'AssetController/process_delete';

$route['autoreplywa'] = 'AutoreplywaController/index';
$route['autoreplywa/datatables'] = 'AutoreplywaController/datatables';
$route['autoreplywa/add'] = 'AutoreplywaController/add';
$route['autoreplywa/add/process'] = 'AutoreplywaController/process_add';
$route['autoreplywa/edit/(:num)'] = 'AutoreplywaController/edit/$1';
$route['autoreplywa/edit/(:num)/process'] = 'AutoreplywaController/process_edit/$1';
$route['autoreplywa/delete'] = 'AutoreplywaController/process_delete';

$route['highlight'] = 'HighlightController';
$route['highlight/datatables'] = 'HighlightController/datatables';
$route['highlight/add'] = 'HighlightController/add';
$route['highlight/add/process'] = 'HighlightController/process_add';
$route['highlight/delete'] = 'HighlightController/process_delete';
$route['highlight/edit/(:num)'] = 'HighlightController/edit/$1';
$route['highlight/edit/(:num)/process'] = 'HighlightController/process_edit/$1';
$route['highlight/content/(:num)'] = 'HighlightController/content/$1';

$route['floworganizationchart'] = 'FlowOrganizationChart';
$route['floworganizationchart/add'] = 'FlowOrganizationChart/add';
$route['floworganizationchart/add/process'] = 'FlowOrganizationChart/process_add';
$route['floworganizationchart/delete'] = 'FlowOrganizationChart/process_delete';
$route['floworganizationchart/edit/(:num)'] = 'FlowOrganizationChart/edit/$1';
$route['floworganizationchart/edit/(:num)/process'] = 'FlowOrganizationChart/process_edit/$1';

$route['strukturorganisasi'] = 'StrukturOrganisasi';
$route['strukturorganisasi/datatables'] = 'StrukturOrganisasi/datatables';
$route['strukturorganisasi/add'] = 'StrukturOrganisasi/add';
$route['strukturorganisasi/add/process'] = 'StrukturOrganisasi/process_add';
$route['strukturorganisasi/edit/(:num)'] = 'StrukturOrganisasi/edit/$1';
$route['strukturorganisasi/edit/(:num)/process'] = 'StrukturOrganisasi/process_edit/$1';
$route['strukturorganisasi/delete'] = 'StrukturOrganisasi/process_delete';
$route['strukturorganisasi/download'] = 'StrukturOrganisasi/download_strukturorganisasi';
$route['strukturorganisasi/permohonan'] = 'StrukturOrganisasi/permohonan';
$route['strukturorganisasi/permohonan/detail'] = 'StrukturOrganisasi/permohonan_detail';
$route['strukturorganisasi/permohonan/detail/approve'] = 'StrukturOrganisasi/permohonan_approve';
$route['strukturorganisasi/permohonan/detail/reject'] = 'StrukturOrganisasi/permohonan_reject';
$route['strukturorganisasi/permohonan/detail/reject/process'] = 'StrukturOrganisasi/permohonan_reject_process';
$route['strukturorganisasi/sync'] = 'StrukturOrganisasi/sync';

$route['penandatangan'] = 'Penandatangan';
$route['penandatangan/add'] = 'Penandatangan/add';
$route['penandatangan/add/process'] = 'Penandatangan/process_add';
$route['penandatangan/datatables'] = 'Penandatangan/datatables';
$route['penandatangan/delete'] = 'Penandatangan/process_delete';
$route['penandatangan/edit/(:num)'] = 'Penandatangan/edit/$1';
$route['penandatangan/edit/(:num)/process'] = 'Penandatangan/process_edit/$1';

$route['arsip/pengaduan'] = 'ArsipPengaduan';

$route['arsip/guestbook'] = 'ArsipGuestbook';
$route['arsip/guestbook/export'] = 'ArsipGuestbook/export';
$route['arsip/guestbook/approve'] = 'ArsipGuestbook/approve';
$route['arsip/guestbook/reschedule'] = 'ArsipGuestbook/reschedule';
$route['arsip/guestbook/reschedule/process'] = 'ArsipGuestbook/process_reschedule';

$route['pmd/leader'] = 'PMDLeader';
$route['pmd/leader/add'] = 'PMDLeader/add';
$route['pmd/leader/add/process'] = 'PMDLeader/process_add';
$route['pmd/leader/edit/(:num)'] = 'PMDLeader/edit/$1';
$route['pmd/leader/edit/(:num)/process'] = 'PMDLeader/process_edit/$1';
$route['pmd/leader/delete'] = 'PMDLeader/process_delete';

$route['pmd/member'] = 'PMDMember';
$route['pmd/member/add'] = 'PMDMember/add';
$route['pmd/member/add/process'] = 'PMDMember/process_add';
$route['pmd/member/delete'] = 'PMDMember/process_delete';
$route['pmd/member/edit/(:num)'] = 'PMDMember/edit/$1';
$route['pmd/member/edit/(:num)/process'] = 'PMDMember/process_edit/$1';

$route['pmd/disposisi'] = 'PMDDisposisi';
$route['pmd/disposisi/add'] = 'PMDDisposisi/add';
$route['pmd/disposisi/add/process'] = 'PMDDisposisi/process_add';
$route['pmd/disposisi/edit/(:num)'] = 'PMDDisposisi/edit/$1';
$route['pmd/disposisi/edit/(:num)/process'] = 'PMDDisposisi/process_edit/$1';
$route['pmd/disposisi/delete'] = 'PMDDisposisi/process_delete';
$route['pmd/disposisi/document/(:num)'] = 'PMDDisposisi/document/$1';
$route['pmd/disposisi/documentresult/(:num)'] = 'PMDDisposisi/documentresult/$1';
$route['pmd/disposisi/track/(:num)'] = 'PMDDisposisi/track/$1';
$route['pmd/approval'] = 'PMDDisposisi/approval';
$route['pmd/approval/set'] = 'PMDDisposisi/set_approval';
$route['pmd/approval/barcode'] = 'PMDDisposisi/barcode';
$route['pmd/approval/barcode/process'] = 'PMDDisposisi/process_barcode';
$route['pmd/approval/digital'] = 'PMDDisposisi/digital';
$route['pmd/approval/digital/process'] = 'PMDDisposisi/process_digital';

$route['database/siskeudes/tapin'] = 'DatabaseSiskeudesTapin';
$route['database/siskeudes/tapin/data'] = 'DatabaseSiskeudesTapin/data';
$route['database/siskeudes/tapin/data/datatables'] = 'DatabaseSiskeudesTapin/datatables';

$route['timelinetask'] = 'TimelineTask';
$route['timelinetask/add'] = 'TimelineTask/add';
$route['timelinetask/add/process'] = 'TimelineTask/process_add';
$route['timelinetask/edit/(:num)'] = 'TimelineTask/edit/$1';
$route['timelinetask/edit/(:num)/process'] = 'TimelineTask/process_edit/$1';
$route['timelinetask/delete'] = 'TimelineTask/process_delete';
$route['timelinetask/release'] = 'TimelineTask/release';
$route['timelinetask/send_document'] = 'TimelineTask/send_document';
$route['timelinetask/send_document/process'] = 'TimelineTask/process_send_document';
$route['timelinetask/request'] = 'TimelineTask/request';
$route['timelinetask/request/verification'] = 'TimelineTask/verification';

$route['operator_desa'] = 'OperatorDesa';
$route['operator_desa/add'] = 'OperatorDesa/add';
$route['operator_desa/add/process'] = 'OperatorDesa/process_add';
$route['operator_desa/edit/(:num)'] = 'OperatorDesa/edit/$1';
$route['operator_desa/edit/(:num)/process'] = 'OperatorDesa/process_edit/$1';
$route['operator_desa/delete'] = 'OperatorDesa/process_delete';

$route['whatsappgateway'] = 'WhatsAppGateway';
$route['whatsappgateway/qr'] = 'WhatsAppGateway/qr';
$route['whatsappgateway/status'] = 'WhatsAppGateway/status';

$route['theme'] = 'Theme';
$route['theme/set'] = 'Theme/set';

$route['siades'] = 'Siades';
$route['siades/process'] = 'Siades/process';
$route['siades/export/attendance/recap'] = 'Siades/export_attendance_recap';
$route['siades/get/attendance/recap'] = 'Siades/get_attendance_recap';

$route['master/(:any)'] = 'CRUDCore/index/$1';
$route['master/(:any)/add'] = 'CRUDCore/add/$1';
$route['master/(:any)/add/process'] = 'CRUDCore/process_add/$1';
$route['master/(:any)/edit/(:num)'] = 'CRUDCore/edit/$1/$2';
$route['master/(:any)/edit/(:num)/process'] = 'CRUDCore/process_edit/$1/$2';
$route['master/(:any)/delete'] = 'CRUDCore/process_delete/$1';

$route['cronjobs/shares'] = 'Cronjobs/shares';
$route['cronjobs/requestdomain'] = 'Cronjobs/requestdomain';
$route['cronjobs/cloudflare'] = 'Cronjobs/cloudflare';
$route['cronjobs/cloudflare/pair'] = 'Cronjobs/cloudflare_pair';
$route['cronjobs/autossl'] = 'Cronjobs/auto_ssl';
$route['cronjobs/migrate/uploads'] = 'Cronjobs/migrate_uploads';
$route['cronjobs/reminder/timelinetask'] = 'Cronjobs/reminder_timelinetask';

$route['database/backups'] = 'DatabaseController/backups';

$route['adsense/reports'] = 'AdsenseController/reports';

$route['uploader/upload'] = 'Uploader/upload';

$route['filemanager/browse'] = 'FileManager/browse';

$route['gidesai/start'] = 'Gidesai/start';

$route['fb/save_token'] = 'Firebase/save_token';

$route['callback/wablas'] = 'Callback/wablas';
$route['callback/whatsapp'] = 'Callback/whatsapp';
