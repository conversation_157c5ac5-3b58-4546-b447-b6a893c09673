# Fitur Penerimaan BLT (Bantuan Langsung Tunai)

## Deskripsi
Fitur ini memungkinkan akses desa untuk mengelola data penerimaan BLT (Bantuan Langsung Tunai) dengan sistem verifikasi. Fitur ini mencakup penambahan, pengeditan, dan pengelolaan status verifikasi penerima BLT.

## Struktur Database

### Tabel `penerimaan_blt`
- `id` (int, primary key, auto increment)
- `deskripsi` (text, deskripsi BLT)
- `nominal` (decimal 15,2, nominal bantuan)
- `tanggal_dibuat` (datetime, default CURRENT_TIMESTAMP)
- `tanggal_diubah` (datetime, default CURRENT_TIMESTAMP ON UPDATE)
- `id_user` (int, foreign key ke tabel users)

### Tabel `detail_penerima_blt`
- `id` (int, primary key, auto increment)
- `id_penerimaan_blt` (int, foreign key ke tabel penerimaan_blt)
- `nik` (varchar 16, foreign key ke tabel warga)
- `status_verifikasi` (enum: 'menunggu_verifikasi', 'terverifikasi')
- `tanggal_verifikasi` (datetime, nullable)
- `tanggal_dibuat` (datetime, default CURRENT_TIMESTAMP)
- `tanggal_diubah` (datetime, default CURRENT_TIMESTAMP ON UPDATE)

## Fitur Utama

### 1. Index/Daftar Penerimaan BLT
- **URL**: `/penerimaan_blt`
- **Fungsi**: Menampilkan daftar semua penerimaan BLT dengan datatables
- **Kolom yang ditampilkan**:
  - Deskripsi BLT
  - Nominal bantuan
  - Total penerima
  - Jumlah yang sudah terverifikasi
  - Jumlah yang menunggu verifikasi
  - Tanggal dibuat
  - Aksi (Detail, Edit, Hapus)

### 2. Tambah Penerimaan BLT
- **URL**: `/penerimaan_blt/add`
- **Fungsi**: Form untuk menambah penerimaan BLT baru
- **Input**:
  - Deskripsi BLT (textarea, required)
  - Nominal BLT (number, required, min: 1)
  - Pilihan warga penerima (checklist dengan fitur search dan select all)
- **Validasi**:
  - Deskripsi tidak boleh kosong
  - Nominal harus lebih dari 0
  - Minimal satu warga harus dipilih

### 3. Edit Penerimaan BLT
- **URL**: `/penerimaan_blt/edit/{id}`
- **Fungsi**: Form untuk mengedit penerimaan BLT yang sudah ada
- **Fitur**:
  - Pre-fill data yang sudah ada
  - Pre-select warga yang sudah terpilih sebelumnya
  - Validasi sama dengan form tambah

### 4. Detail Penerimaan BLT
- **URL**: `/penerimaan_blt/detail/{id}`
- **Fungsi**: Menampilkan detail penerimaan BLT dan mengelola verifikasi
- **Fitur**:
  - Informasi BLT (deskripsi, nominal, tanggal dibuat)
  - Statistik penerima (total, terverifikasi, menunggu)
  - Daftar penerima dengan status verifikasi
  - Tombol untuk mengubah status verifikasi per penerima

## Status Verifikasi

### Menunggu Verifikasi
- Status default saat data BLT pertama kali dibuat
- Menandakan bahwa warga belum menerima bantuan
- Ditampilkan dengan badge warning (kuning)

### Terverifikasi
- Status setelah admin desa mengkonfirmasi bahwa warga sudah menerima bantuan
- Tanggal verifikasi akan tercatat otomatis
- Ditampilkan dengan badge success (hijau)
- Status dapat dikembalikan ke "menunggu verifikasi" jika diperlukan

## Akses dan Keamanan
- Hanya user dengan role 'admin' (akses desa) yang dapat mengakses fitur ini
- Semua data dibatasi berdasarkan `id_user` (sesuai dengan desa yang login)
- Validasi session dan role dilakukan di setiap endpoint

## File yang Dibuat/Dimodifikasi

### Controller
- `app/application/controllers/Penerimaan_BLT.php`

### Model
- `app/application/models/Penerimaan_BLT_Model.php`
- `app/application/models/Detail_Penerima_BLT_Model.php`

### View
- `app/application/views/penerimaan_blt/index.php`
- `app/application/views/penerimaan_blt/add.php`
- `app/application/views/penerimaan_blt/edit.php`
- `app/application/views/penerimaan_blt/detail.php`

### Routes
- Ditambahkan routing di `app/application/config/routes.php`

### Database Schema
- `database_blt.sql` (file SQL untuk membuat tabel)

## Cara Instalasi

1. **Jalankan SQL Schema**:
   ```sql
   -- Jalankan isi file database_blt.sql di database Anda
   ```

2. **Upload File**:
   - Upload semua file controller, model, dan view ke direktori yang sesuai

3. **Update Routes**:
   - Routes sudah ditambahkan di config/routes.php

4. **Akses Fitur**:
   - Login sebagai admin desa
   - Akses URL `/penerimaan_blt`

## Teknologi yang Digunakan
- **Backend**: CodeIgniter 3
- **Frontend**: Bootstrap 4, jQuery, DataTables
- **Database**: MySQL
- **Notifikasi**: Toastr
- **Icons**: Font Awesome

## Catatan Pengembangan
- Fitur ini mengikuti pola MVC yang sudah ada di aplikasi
- Menggunakan helper functions yang sudah tersedia (isLogin, isAdmin, getCurrentIdUser, dll.)
- Responsive design dengan Bootstrap
- AJAX untuk operasi CRUD tanpa reload halaman
- Validasi client-side dan server-side
